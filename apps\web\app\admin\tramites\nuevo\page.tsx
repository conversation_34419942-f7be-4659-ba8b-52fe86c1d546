'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  ArrowLeft,
  Save,
  Plus,
  X
} from 'lucide-react';
import Link from 'next/link';

interface Dependencia {
  id: string;
  nombre: string;
  subdependencias?: Subdependencia[];
}

interface Subdependencia {
  id: string;
  nombre: string;
}

interface TramiteForm {
  nombre: string;
  descripcion: string;
  categoria: string;
  dependencia_id: string;
  subdependencia_id: string;
  tiempo_estimado: string;
  costo: string;
  requisitos: string[];
  documentos_requeridos: string[];
  modalidad: string;
  estado: string;
  observaciones: string;
  normativa: string;
}

export default function NuevoTramitePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [dependencias, setDependencias] = useState<Dependencia[]>([]);
  const [subdependencias, setSubdependencias] = useState<Subdependencia[]>([]);
  const [newRequisito, setNewRequisito] = useState('');
  const [newDocumento, setNewDocumento] = useState('');
  
  const [formData, setFormData] = useState<TramiteForm>({
    nombre: '',
    descripcion: '',
    categoria: '',
    dependencia_id: '',
    subdependencia_id: '',
    tiempo_estimado: '',
    costo: '',
    requisitos: [],
    documentos_requeridos: [],
    modalidad: 'presencial',
    estado: 'activo',
    observaciones: '',
    normativa: ''
  });

  useEffect(() => {
    fetchDependencias();
  }, []);

  useEffect(() => {
    if (formData.dependencia_id) {
      fetchSubdependencias(formData.dependencia_id);
    } else {
      setSubdependencias([]);
      setFormData(prev => ({ ...prev, subdependencia_id: '' }));
    }
  }, [formData.dependencia_id]);

  const fetchDependencias = async () => {
    try {
      const response = await fetch('/api/dependencias?limit=100');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDependencias(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching dependencias:', error);
    }
  };

  const fetchSubdependencias = async (dependenciaId: string) => {
    try {
      const response = await fetch(`/api/dependencias/${dependenciaId}/subdependencias`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSubdependencias(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching subdependencias:', error);
    }
  };

  const handleInputChange = (field: keyof TramiteForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addRequisito = () => {
    if (newRequisito.trim()) {
      setFormData(prev => ({
        ...prev,
        requisitos: [...prev.requisitos, newRequisito.trim()]
      }));
      setNewRequisito('');
    }
  };

  const removeRequisito = (index: number) => {
    setFormData(prev => ({
      ...prev,
      requisitos: prev.requisitos.filter((_, i) => i !== index)
    }));
  };

  const addDocumento = () => {
    if (newDocumento.trim()) {
      setFormData(prev => ({
        ...prev,
        documentos_requeridos: [...prev.documentos_requeridos, newDocumento.trim()]
      }));
      setNewDocumento('');
    }
  };

  const removeDocumento = (index: number) => {
    setFormData(prev => ({
      ...prev,
      documentos_requeridos: prev.documentos_requeridos.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.nombre || !formData.dependencia_id) {
      alert('Por favor completa los campos obligatorios');
      return;
    }

    try {
      setLoading(true);
      
      const response = await fetch('/api/tramites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          router.push('/admin/tramites');
        } else {
          alert(data.error || 'Error al crear el trámite');
        }
      } else {
        alert('Error al crear el trámite');
      }
    } catch (error) {
      console.error('Error creating tramite:', error);
      alert('Error al crear el trámite');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/admin/tramites">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Nuevo Trámite</h1>
          <p className="text-gray-600">Crear un nuevo trámite municipal</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Información Básica */}
        <Card>
          <CardHeader>
            <CardTitle>Información Básica</CardTitle>
            <CardDescription>
              Datos principales del trámite
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="nombre">Nombre del Trámite *</Label>
                <Input
                  id="nombre"
                  value={formData.nombre}
                  onChange={(e) => handleInputChange('nombre', e.target.value)}
                  placeholder="Ej: Certificado de Residencia"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="categoria">Categoría</Label>
                <Input
                  id="categoria"
                  value={formData.categoria}
                  onChange={(e) => handleInputChange('categoria', e.target.value)}
                  placeholder="Ej: Certificados"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="descripcion">Descripción</Label>
              <Textarea
                id="descripcion"
                value={formData.descripcion}
                onChange={(e) => handleInputChange('descripcion', e.target.value)}
                placeholder="Describe brevemente el trámite..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Organización */}
        <Card>
          <CardHeader>
            <CardTitle>Organización</CardTitle>
            <CardDescription>
              Dependencia responsable del trámite
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="dependencia">Dependencia *</Label>
                <Select 
                  value={formData.dependencia_id} 
                  onValueChange={(value) => handleInputChange('dependencia_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona una dependencia" />
                  </SelectTrigger>
                  <SelectContent>
                    {dependencias.map((dep) => (
                      <SelectItem key={dep.id} value={dep.id}>
                        {dep.nombre}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="subdependencia">Subdependencia</Label>
                <Select 
                  value={formData.subdependencia_id} 
                  onValueChange={(value) => handleInputChange('subdependencia_id', value)}
                  disabled={!formData.dependencia_id || subdependencias.length === 0}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona una subdependencia" />
                  </SelectTrigger>
                  <SelectContent>
                    {subdependencias.map((sub) => (
                      <SelectItem key={sub.id} value={sub.id}>
                        {sub.nombre}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detalles del Trámite */}
        <Card>
          <CardHeader>
            <CardTitle>Detalles del Trámite</CardTitle>
            <CardDescription>
              Información específica sobre tiempo, costo y modalidad
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="tiempo_estimado">Tiempo Estimado</Label>
                <Input
                  id="tiempo_estimado"
                  value={formData.tiempo_estimado}
                  onChange={(e) => handleInputChange('tiempo_estimado', e.target.value)}
                  placeholder="Ej: 3-5 días hábiles"
                />
              </div>
              
              <div>
                <Label htmlFor="costo">Costo</Label>
                <Input
                  id="costo"
                  value={formData.costo}
                  onChange={(e) => handleInputChange('costo', e.target.value)}
                  placeholder="Ej: $15,000 COP"
                />
              </div>
              
              <div>
                <Label htmlFor="modalidad">Modalidad</Label>
                <Select 
                  value={formData.modalidad} 
                  onValueChange={(value) => handleInputChange('modalidad', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="presencial">Presencial</SelectItem>
                    <SelectItem value="virtual">Virtual</SelectItem>
                    <SelectItem value="mixta">Mixta</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="estado">Estado</Label>
              <Select 
                value={formData.estado} 
                onValueChange={(value) => handleInputChange('estado', value)}
              >
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="activo">Activo</SelectItem>
                  <SelectItem value="inactivo">Inactivo</SelectItem>
                  <SelectItem value="borrador">Borrador</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Requisitos */}
        <Card>
          <CardHeader>
            <CardTitle>Requisitos</CardTitle>
            <CardDescription>
              Lista de requisitos necesarios para el trámite
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={newRequisito}
                onChange={(e) => setNewRequisito(e.target.value)}
                placeholder="Agregar nuevo requisito..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addRequisito())}
              />
              <Button type="button" onClick={addRequisito}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            {formData.requisitos.length > 0 && (
              <div className="space-y-2">
                {formData.requisitos.map((requisito, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm">{requisito}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRequisito(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Documentos Requeridos */}
        <Card>
          <CardHeader>
            <CardTitle>Documentos Requeridos</CardTitle>
            <CardDescription>
              Documentos que debe presentar el ciudadano
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <Input
                value={newDocumento}
                onChange={(e) => setNewDocumento(e.target.value)}
                placeholder="Agregar nuevo documento..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDocumento())}
              />
              <Button type="button" onClick={addDocumento}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            {formData.documentos_requeridos.length > 0 && (
              <div className="space-y-2">
                {formData.documentos_requeridos.map((documento, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm">{documento}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeDocumento(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Información Adicional */}
        <Card>
          <CardHeader>
            <CardTitle>Información Adicional</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="observaciones">Observaciones</Label>
              <Textarea
                id="observaciones"
                value={formData.observaciones}
                onChange={(e) => handleInputChange('observaciones', e.target.value)}
                placeholder="Observaciones adicionales..."
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="normativa">Marco Normativo</Label>
              <Textarea
                id="normativa"
                value={formData.normativa}
                onChange={(e) => handleInputChange('normativa', e.target.value)}
                placeholder="Leyes, decretos o resoluciones que aplican..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Link href="/admin/tramites">
            <Button variant="outline" type="button">
              Cancelar
            </Button>
          </Link>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Guardando...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Crear Trámite
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
