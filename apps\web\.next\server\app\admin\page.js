(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={2064:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>l,pages:()=>p,routeModule:()=>c,tree:()=>d});var n=t(24332),s=t(48819),a=t(67851),o=t.n(a),i=t(97540),u={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>i[e]);t.d(r,u);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,16991)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,47672)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6558:!1,10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16991:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20505:(e,r,t)=>{Promise.resolve().then(t.bind(t,16991))},26791:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var n=t(13486);t(60159);var s=t(76353),a=t(67499);let o=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:r,...t}){return(0,n.jsx)("div",{className:(0,a.cn)(o({variant:r}),e),...t})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},56953:(e,r,t)=>{Promise.resolve().then(t.bind(t,6558))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[191,7118,114,684,5194,7269],()=>t(2064));module.exports=n})();