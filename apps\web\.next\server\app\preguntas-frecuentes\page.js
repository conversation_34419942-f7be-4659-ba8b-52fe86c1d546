(()=>{var e={};e.id=6701,e.ids=[6701],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32514:!1,33873:e=>{"use strict";e.exports=require("path")},35744:(e,r,s)=>{Promise.resolve().then(s.bind(s,51354))},36151:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60159);let a=t.forwardRef(function({title:e,titleId:r,...s},a){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),e?t.createElement("title",{id:r},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},37666:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60159);let a=t.forwardRef(function({title:e,titleId:r,...s},a){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),e?t.createElement("title",{id:r},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},51354:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m});var t=s(13486),a=s(60159),n=s(49989),l=s.n(n),o=s(32514),i=s(36151),c=s(54575),d=s(38149),u=s(37666),x=s(85480),p=s(61817);function m(){let[e,r]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[m,h]=(0,a.useState)(null),[g,f]=(0,a.useState)(""),[b,j]=(0,a.useState)(""),[v,y]=(0,a.useState)(null),[w,N]=(0,a.useState)([]),k=async()=>{try{n(!0);let e=new URLSearchParams;g&&e.append("search",g),b&&e.append("tema",b),e.append("limit","50");let s=await fetch(`/api/faqs?${e}`);if(!s.ok)throw Error("Error al cargar las preguntas frecuentes");let t=await s.json();r(t.data)}catch(e){h(e instanceof Error?e.message:"Error desconocido")}finally{n(!1)}},P=e=>{y(v===e?null:e)},C=()=>{f(""),j(""),y(null)};return(0,t.jsxs)(o.default,{className:"bg-gray-50",children:[(0,t.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsx)("nav",{className:"flex","aria-label":"Breadcrumb",children:(0,t.jsxs)("ol",{className:"flex items-center space-x-4",children:[(0,t.jsx)("li",{children:(0,t.jsx)(l(),{href:"/",className:"text-gray-400 hover:text-gray-500",children:"Inicio"})}),(0,t.jsx)("li",{children:(0,t.jsx)(i.A,{className:"h-4 w-4 text-gray-400"})}),(0,t.jsx)("li",{children:(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:"Preguntas Frecuentes"})})]})})})}),(0,t.jsx)("div",{className:"bg-white",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(c.A,{className:"h-16 w-16 text-primary-600 mx-auto mb-6"}),(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Preguntas Frecuentes"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Encuentra respuestas r\xe1pidas a las consultas m\xe1s comunes sobre los servicios municipales de Ch\xeda. Si no encuentras lo que buscas, puedes contactarnos directamente."})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsx)("div",{className:"bg-white rounded-xl shadow-md p-6 mb-8",children:(0,t.jsx)("form",{onSubmit:e=>{e.preventDefault(),k()},className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,t.jsx)("input",{type:"text",placeholder:"Buscar en preguntas y respuestas...",value:g,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"})]})}),(0,t.jsx)("div",{className:"lg:w-80",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,t.jsxs)("select",{value:b,onChange:e=>j(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none bg-white",children:[(0,t.jsx)("option",{value:"",children:"Todos los temas"}),w.map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]})}),(g||b)&&(0,t.jsx)("button",{type:"button",onClick:C,className:"px-4 py-3 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Limpiar"})]})})}),s&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"}),(0,t.jsx)("p",{className:"text-gray-600 mt-4",children:"Cargando preguntas frecuentes..."})]}),m&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 mb-8",children:(0,t.jsx)("p",{className:"text-red-800",children:m})}),!s&&!m&&(0,t.jsx)("div",{className:"space-y-4",children:0===e.length?(0,t.jsxs)("div",{className:"text-center py-12 bg-white rounded-xl shadow-md",children:[(0,t.jsx)(c.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No se encontraron preguntas frecuentes"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Intenta con otros t\xe9rminos de b\xfasqueda o selecciona un tema diferente."}),(0,t.jsx)("button",{onClick:C,className:"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:"Ver todas las preguntas"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Mostrando ",e.length," pregunta",1!==e.length?"s":""," frecuente",1!==e.length?"s":"",g&&` para "${g}"`,b&&` en el tema "${b}"`]})}),e.map(e=>(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-md overflow-hidden",children:[(0,t.jsx)("button",{onClick:()=>P(e.id),className:"w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.pregunta}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,t.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded-full",children:e.tema}),(0,t.jsx)("span",{children:e.dependencia.nombre})]})]}),(0,t.jsx)("div",{className:"ml-4",children:v===e.id?(0,t.jsx)(x.A,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})})]})}),v===e.id&&(0,t.jsx)("div",{className:"px-6 pb-6 border-t border-gray-100",children:(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsx)("div",{className:"prose max-w-none text-gray-700",children:e.respuesta.split("\n").map((e,r)=>(0,t.jsx)("p",{className:"mb-3 last:mb-0",children:e},r))})})})]},e.id))]})}),(0,t.jsxs)("div",{className:"mt-12 bg-white rounded-xl shadow-md p-8 text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\xbfNo encontraste lo que buscabas?"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Nuestro asistente de IA puede ayudarte con consultas espec\xedficas, o puedes contactarnos directamente a trav\xe9s de nuestros canales oficiales."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(l(),{href:"/chat",className:"inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Hablar con el Asistente IA"}),(0,t.jsx)(l(),{href:"/contacto",className:"inline-flex items-center gap-2 border border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Contactar Soporte"})]})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69882:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=s(24332),a=s(48819),n=s(67851),l=s.n(n),o=s(97540),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(r,i);let c={children:["",{children:["preguntas-frecuentes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,89132)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\preguntas-frecuentes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\preguntas-frecuentes\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/preguntas-frecuentes/page",pathname:"/preguntas-frecuentes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},72192:(e,r,s)=>{Promise.resolve().then(s.bind(s,89132))},85480:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60159);let a=t.forwardRef(function({title:e,titleId:r,...s},a){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},s),e?t.createElement("title",{id:r},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))})},89132:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\preguntas-frecuentes\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[191,7118,114,2892,8686],()=>s(69882));module.exports=t})();