/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/preguntas-frecuentes/page";
exports.ids = ["app/preguntas-frecuentes/page"];
exports.modules = {

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpreguntas-frecuentes%2Fpage&page=%2Fpreguntas-frecuentes%2Fpage&appPaths=%2Fpreguntas-frecuentes%2Fpage&pagePath=private-next-app-dir%2Fpreguntas-frecuentes%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpreguntas-frecuentes%2Fpage&page=%2Fpreguntas-frecuentes%2Fpage&appPaths=%2Fpreguntas-frecuentes%2Fpage&pagePath=private-next-app-dir%2Fpreguntas-frecuentes%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?87f3\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/preguntas-frecuentes/page.tsx */ \"(rsc)/./app/preguntas-frecuentes/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'preguntas-frecuentes',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/preguntas-frecuentes/page\",\n        pathname: \"/preguntas-frecuentes\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpreguntas-frecuentes%2Fpage&page=%2Fpreguntas-frecuentes%2Fpage&appPaths=%2Fpreguntas-frecuentes%2Fpage&pagePath=private-next-app-dir%2Fpreguntas-frecuentes%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cpreguntas-frecuentes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cpreguntas-frecuentes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/preguntas-frecuentes/page.tsx */ \"(rsc)/./app/preguntas-frecuentes/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKdWFuJTIwUHVsZ2FyaW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hpYS1uZXh0JTVDJTVDYXBwcyU1QyU1Q3dlYiU1QyU1Q2FwcCU1QyU1Q3ByZWd1bnRhcy1mcmVjdWVudGVzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSnVhbiBQdWxnYXJpblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxjaGlhLW5leHRcXFxcYXBwc1xcXFx3ZWJcXFxcYXBwXFxcXHByZWd1bnRhcy1mcmVjdWVudGVzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cpreguntas-frecuentes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7c3867ab4652\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcSnVhbiBQdWxnYXJpblxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGlhLW5leHRcXGFwcHNcXHdlYlxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjdjMzg2N2FiNDY1MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1\n};\nconst metadata = {\n    metadataBase: new URL('https://portal.chia-cundinamarca.gov.co'),\n    title: {\n        default: 'CHIA - Portal Ciudadano Digital',\n        template: '%s | CHIA - Portal Ciudadano'\n    },\n    description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca. Realiza trámites en línea, consulta información municipal y accede a servicios digitales las 24 horas.',\n    keywords: [\n        'Chía',\n        'Cundinamarca',\n        'servicios ciudadanos',\n        'gobierno digital',\n        'trámites en línea',\n        'certificados',\n        'impuestos',\n        'licencias',\n        'portal ciudadano',\n        'alcaldía',\n        'municipio'\n    ],\n    authors: [\n        {\n            name: 'Alcaldía Municipal de Chía'\n        }\n    ],\n    creator: 'Alcaldía Municipal de Chía',\n    publisher: 'Alcaldía Municipal de Chía',\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    openGraph: {\n        type: 'website',\n        locale: 'es_CO',\n        url: 'https://portal.chia-cundinamarca.gov.co',\n        siteName: 'CHIA - Portal Ciudadano',\n        title: 'CHIA - Portal Ciudadano Digital',\n        description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca. Trámites en línea las 24 horas.',\n        images: [\n            {\n                url: '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: 'Portal Ciudadano de Chía'\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'CHIA - Portal Ciudadano Digital',\n        description: 'Portal oficial de servicios ciudadanos para el municipio de Chía, Cundinamarca.',\n        images: [\n            '/og-image.jpg'\n        ],\n        creator: '@AlcaldiaChia'\n    },\n    alternates: {\n        canonical: 'https://portal.chia-cundinamarca.gov.co'\n    },\n    category: 'government'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} h-full`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"root\",\n                className: \"min-h-full\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\layout.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\layout.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\layout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/preguntas-frecuentes/page.tsx":
/*!*******************************************!*\
  !*** ./app/preguntas-frecuentes/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\preguntas-frecuentes\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cpreguntas-frecuentes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cpreguntas-frecuentes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/preguntas-frecuentes/page.tsx */ \"(ssr)/./app/preguntas-frecuentes/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNKdWFuJTIwUHVsZ2FyaW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hpYS1uZXh0JTVDJTVDYXBwcyU1QyU1Q3dlYiU1QyU1Q2FwcCU1QyU1Q3ByZWd1bnRhcy1mcmVjdWVudGVzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSnVhbiBQdWxnYXJpblxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxjaGlhLW5leHRcXFxcYXBwc1xcXFx3ZWJcXFxcYXBwXFxcXHByZWd1bnRhcy1mcmVjdWVudGVzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cpreguntas-frecuentes%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CJuan%20Pulgarin%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchia-next%5C%5Capps%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/preguntas-frecuentes/page.tsx":
/*!*******************************************!*\
  !*** ./app/preguntas-frecuentes/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PreguntasFrecuentesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_layout_PageLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/PageLayout */ \"(ssr)/./components/layout/PageLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChevronDownIcon,ChevronUpIcon,FunnelIcon,MagnifyingGlassIcon,QuestionMarkCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChevronDownIcon,ChevronUpIcon,FunnelIcon,MagnifyingGlassIcon,QuestionMarkCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/QuestionMarkCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChevronDownIcon,ChevronUpIcon,FunnelIcon,MagnifyingGlassIcon,QuestionMarkCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChevronDownIcon,ChevronUpIcon,FunnelIcon,MagnifyingGlassIcon,QuestionMarkCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChevronDownIcon,ChevronUpIcon,FunnelIcon,MagnifyingGlassIcon,QuestionMarkCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChevronDownIcon,ChevronUpIcon,FunnelIcon,MagnifyingGlassIcon,QuestionMarkCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction PreguntasFrecuentesPage() {\n    const [faqs, setFaqs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedTema, setSelectedTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [expandedFAQ, setExpandedFAQ] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temas, setTemas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch FAQs\n    const fetchFAQs = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (searchQuery) params.append('search', searchQuery);\n            if (selectedTema) params.append('tema', selectedTema);\n            params.append('limit', '50'); // Show more FAQs\n            const response = await fetch(`/api/faqs?${params}`);\n            if (!response.ok) {\n                throw new Error('Error al cargar las preguntas frecuentes');\n            }\n            const data = await response.json();\n            setFaqs(data.data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Error desconocido');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch unique topics for filtering\n    const fetchTemas = async ()=>{\n        try {\n            const uniqueTemas = [\n                ...new Set(faqs.map((faq)=>faq.tema))\n            ].filter(Boolean);\n            setTemas(uniqueTemas.sort());\n        } catch (err) {\n            console.error('Error fetching topics:', err);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PreguntasFrecuentesPage.useEffect\": ()=>{\n            fetchFAQs();\n        }\n    }[\"PreguntasFrecuentesPage.useEffect\"], [\n        searchQuery,\n        selectedTema\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PreguntasFrecuentesPage.useEffect\": ()=>{\n            if (faqs.length > 0) {\n                fetchTemas();\n            }\n        }\n    }[\"PreguntasFrecuentesPage.useEffect\"], [\n        faqs\n    ]);\n    const toggleFAQ = (faqId)=>{\n        setExpandedFAQ(expandedFAQ === faqId ? null : faqId);\n    };\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        fetchFAQs();\n    };\n    const clearFilters = ()=>{\n        setSearchQuery('');\n        setSelectedTema('');\n        setExpandedFAQ(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_PageLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        className: \"bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex\",\n                        \"aria-label\": \"Breadcrumb\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"text-gray-400 hover:text-gray-500\",\n                                        children: \"Inicio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-900 font-medium\",\n                                        children: \"Preguntas Frecuentes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-16 w-16 text-primary-600 mx-auto mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Preguntas Frecuentes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Encuentra respuestas r\\xe1pidas a las consultas m\\xe1s comunes sobre los servicios municipales de Ch\\xeda. Si no encuentras lo que buscas, puedes contactarnos directamente.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-md p-6 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSearch,\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Buscar en preguntas y respuestas...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: selectedTema,\n                                                    onChange: (e)=>setSelectedTema(e.target.value),\n                                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none bg-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Todos los temas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        temas.map((tema)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: tema,\n                                                                children: tema\n                                                            }, tema, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    (searchQuery || selectedTema) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: clearFilters,\n                                        className: \"px-4 py-3 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                        children: \"Limpiar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-4\",\n                                children: \"Cargando preguntas frecuentes...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-6 mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: faqs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12 bg-white rounded-xl shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                    children: \"No se encontraron preguntas frecuentes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: \"Intenta con otros t\\xe9rminos de b\\xfasqueda o selecciona un tema diferente.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearFilters,\n                                    className: \"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors\",\n                                    children: \"Ver todas las preguntas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"Mostrando \",\n                                            faqs.length,\n                                            \" pregunta\",\n                                            faqs.length !== 1 ? 's' : '',\n                                            \" frecuente\",\n                                            faqs.length !== 1 ? 's' : '',\n                                            searchQuery && ` para \"${searchQuery}\"`,\n                                            selectedTema && ` en el tema \"${selectedTema}\"`\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 17\n                                }, this),\n                                faqs.map((faq)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-md overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFAQ(faq.id),\n                                                className: \"w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                    children: faq.pregunta\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-4 text-sm text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"bg-primary-100 text-primary-800 px-2 py-1 rounded-full\",\n                                                                            children: faq.tema\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                                            lineNumber: 252,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: faq.dependencia.nombre\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: expandedFAQ === faq.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChevronDownIcon_ChevronUpIcon_FunnelIcon_MagnifyingGlassIcon_QuestionMarkCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, this),\n                                            expandedFAQ === faq.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6 border-t border-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"prose max-w-none text-gray-700\",\n                                                        children: faq.respuesta.split('\\n').map((paragraph, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-3 last:mb-0\",\n                                                                children: paragraph\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 31\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, faq.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-white rounded-xl shadow-md p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"\\xbfNo encontraste lo que buscabas?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6 max-w-2xl mx-auto\",\n                                children: \"Nuestro asistente de IA puede ayudarte con consultas espec\\xedficas, o puedes contactarnos directamente a trav\\xe9s de nuestros canales oficiales.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/chat\",\n                                        className: \"inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n                                        children: \"Hablar con el Asistente IA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contacto\",\n                                        className: \"inline-flex items-center gap-2 border border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                        children: \"Contactar Soporte\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\preguntas-frecuentes\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/preguntas-frecuentes/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/IdentificationIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChatBubbleLeftRightIcon,CreditCardIcon,DocumentTextIcon,EnvelopeIcon,GlobeAltIcon,IdentificationIcon,MagnifyingGlassIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const quickLinks = [\n        {\n            name: 'Inicio',\n            href: '/'\n        },\n        {\n            name: 'Servicios',\n            href: '/servicios'\n        },\n        {\n            name: 'Asistente IA',\n            href: '/chat'\n        },\n        {\n            name: 'Contacto',\n            href: '/contacto'\n        },\n        {\n            name: 'Acerca de',\n            href: '/acerca'\n        }\n    ];\n    const services = [\n        {\n            name: 'Certificados',\n            href: '/servicios/certificados',\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            name: 'Pagos en Línea',\n            href: '/servicios/pagos',\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            name: 'Licencias',\n            href: '/servicios/licencias',\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: 'Registro Civil',\n            href: '/servicios/registro',\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: 'Consultas',\n            href: '/servicios/consultas',\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        }\n    ];\n    const contactInfo = [\n        {\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Teléfono',\n            value: '(*************',\n            href: 'tel:+576018845911'\n        },\n        {\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: 'Email',\n            value: '<EMAIL>',\n            href: 'mailto:<EMAIL>'\n        },\n        {\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            label: 'Dirección',\n            value: 'Carrera 11 No. 17-25, Chía, Cundinamarca',\n            href: 'https://maps.google.com/?q=Carrera+11+No.+17-25+Chía+Cundinamarca'\n        },\n        {\n            icon: _barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'Sitio Web',\n            value: 'www.chia-cundinamarca.gov.co',\n            href: 'https://www.chia-cundinamarca.gov.co'\n        }\n    ];\n    const legalLinks = [\n        {\n            name: 'Términos y Condiciones',\n            href: '/terminos'\n        },\n        {\n            name: 'Política de Privacidad',\n            href: '/privacidad'\n        },\n        {\n            name: 'Política de Tratamiento de Datos',\n            href: '/datos'\n        },\n        {\n            name: 'Mapa del Sitio',\n            href: '/sitemap'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"C\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: \"CHIA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-300 block leading-none\",\n                                                    children: \"Portal Ciudadano\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm mb-6\",\n                                    children: \"Portal oficial de servicios ciudadanos para el municipio de Ch\\xeda, Cundinamarca. Transformando la relaci\\xf3n entre ciudadanos y gobierno a trav\\xe9s de la innovaci\\xf3n digital.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/chat\",\n                                    className: \"inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_ChatBubbleLeftRightIcon_CreditCardIcon_DocumentTextIcon_EnvelopeIcon_GlobeAltIcon_IdentificationIcon_MagnifyingGlassIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Asistente IA 24/7\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Enlaces R\\xe1pidos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white text-sm transition-colors focus:outline-none focus:text-white\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Servicios\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: service.href,\n                                                className: \"flex items-center gap-2 text-gray-300 hover:text-white text-sm transition-colors focus:outline-none focus:text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    service.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, service.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Contacto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: contactInfo.map((contact)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: contact.href,\n                                                target: contact.href.startsWith('http') ? '_blank' : undefined,\n                                                rel: contact.href.startsWith('http') ? 'noopener noreferrer' : undefined,\n                                                className: \"flex items-start gap-2 text-gray-300 hover:text-white text-sm transition-colors focus:outline-none focus:text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contact.icon, {\n                                                        className: \"h-4 w-4 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: contact.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: contact.value\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, contact.label, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center md:justify-start gap-4\",\n                                children: legalLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: link.href,\n                                        className: \"text-gray-400 hover:text-white text-xs transition-colors focus:outline-none focus:text-white\",\n                                        children: link.name\n                                    }, link.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center md:text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-xs\",\n                                        children: [\n                                            \"\\xa9 \",\n                                            currentYear,\n                                            \" Municipio de Ch\\xeda, Cundinamarca. Todos los derechos reservados.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-xs mt-1\",\n                                        children: \"Desarrollado con ❤️ para los ciudadanos de Ch\\xeda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 pt-6 border-t border-gray-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-xs mb-2\",\n                                children: \"Portal oficial del gobierno municipal - Cumple con est\\xe1ndares de accesibilidad WCAG 2.2 AA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center gap-4 text-xs text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDD12 Sitio Seguro SSL\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"♿ Accesible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCF1 Responsive\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83E\\uDD16 IA Integrada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/MainNavigation.tsx":
/*!**********************************************!*\
  !*** ./components/layout/MainNavigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/BuildingOffice2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/QuestionMarkCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BuildingOffice2Icon,ChatBubbleLeftRightIcon,ChevronDownIcon,ClipboardDocumentListIcon,DocumentTextIcon,HomeIcon,InformationCircleIcon,MagnifyingGlassIcon,PhoneIcon,QuestionMarkCircleIcon,UserIcon,UsersIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/../../node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navigationItems = [\n    {\n        name: 'Inicio',\n        href: '/',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'Dependencias',\n        href: '/dependencias',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Trámites',\n        href: '/tramites',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'OPAs',\n        href: '/opas',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'Servicios',\n        href: '/servicios',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        children: [\n            {\n                name: 'Certificados',\n                href: '/servicios/certificados',\n                description: 'Certificados de residencia, nacimiento, etc.'\n            },\n            {\n                name: 'Pagos en Línea',\n                href: '/servicios/pagos',\n                description: 'Impuestos, multas y tasas municipales'\n            },\n            {\n                name: 'Licencias',\n                href: '/servicios/licencias',\n                description: 'Construcción, funcionamiento, etc.'\n            },\n            {\n                name: 'Registro Civil',\n                href: '/servicios/registro',\n                description: 'Documentos de identidad y registro'\n            },\n            {\n                name: 'Consultas',\n                href: '/servicios/consultas',\n                description: 'Información catastral y municipal'\n            }\n        ]\n    },\n    {\n        name: 'Asistente IA',\n        href: '/chat',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'Preguntas Frecuentes',\n        href: '/preguntas-frecuentes',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'Contacto',\n        href: '/contacto',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'Acerca de',\n        href: '/acerca',\n        icon: _barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\nfunction MainNavigation({ onSearchClick, onChatClick }) {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close mobile menu when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MainNavigation.useEffect\": ()=>{\n            setIsMobileMenuOpen(false);\n            setActiveDropdown(null);\n        }\n    }[\"MainNavigation.useEffect\"], [\n        pathname\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MainNavigation.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"MainNavigation.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setActiveDropdown(null);\n                    }\n                }\n            }[\"MainNavigation.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"MainNavigation.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"MainNavigation.useEffect\"];\n        }\n    }[\"MainNavigation.useEffect\"], []);\n    // Handle keyboard navigation\n    const handleKeyDown = (event, action)=>{\n        if (event.key === 'Enter' || event.key === ' ') {\n            event.preventDefault();\n            action();\n        }\n    };\n    const toggleDropdown = (itemName)=>{\n        setActiveDropdown(activeDropdown === itemName ? null : itemName);\n    };\n    const isActiveLink = (href)=>{\n        if (href === '/') {\n            return pathname === '/';\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40\",\n        role: \"navigation\",\n        \"aria-label\": \"Navegaci\\xf3n principal\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center gap-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg p-1\",\n                                \"aria-label\": \"Ir al inicio - Portal Ciudadano de Ch\\xeda\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"C\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"CHIA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 block leading-none\",\n                                                children: \"Portal Ciudadano\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-1\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: item.children ? dropdownRef : undefined,\n                                    children: item.children ? // Dropdown Menu\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleDropdown(item.name),\n                                                onKeyDown: (e)=>handleKeyDown(e, ()=>toggleDropdown(item.name)),\n                                                className: `flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${isActiveLink(item.href) ? 'text-primary-600 bg-primary-50' : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'}`,\n                                                \"aria-expanded\": activeDropdown === item.name,\n                                                \"aria-haspopup\": \"true\",\n                                                children: [\n                                                    item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    item.name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: `h-4 w-4 transition-transform ${activeDropdown === item.name ? 'rotate-180' : ''}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeDropdown === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-0 mt-1 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: `block px-4 py-3 text-sm hover:bg-gray-50 transition-colors focus:outline-none focus:bg-gray-50 ${isActiveLink(child.href) ? 'text-primary-600 bg-primary-50' : 'text-gray-700'}`,\n                                                        onClick: ()=>setActiveDropdown(null),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: child.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            child.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: child.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, child.name, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 19\n                                    }, this) : // Regular Link\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${isActiveLink(item.href) ? 'text-primary-600 bg-primary-50' : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'}`,\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 35\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onSearchClick,\n                                    className: \"p-2 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n                                    \"aria-label\": \"Buscar servicios\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/login\",\n                                    className: \"flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Ingresar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"p-2 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n                                \"aria-label\": isMobileMenuOpen ? 'Cerrar menú' : 'Abrir menú',\n                                \"aria-expanded\": isMobileMenuOpen,\n                                children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-gray-200 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleDropdown(item.name),\n                                                className: `w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${isActiveLink(item.href) ? 'text-primary-600 bg-primary-50' : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            item.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: `h-4 w-4 transition-transform ${activeDropdown === item.name ? 'rotate-180' : ''}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 23\n                                            }, this),\n                                            activeDropdown === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1 ml-6 space-y-1\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: `block px-3 py-2 rounded-lg text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${isActiveLink(child.href) ? 'text-primary-600 bg-primary-50' : 'text-gray-600 hover:text-primary-600 hover:bg-gray-50'}`,\n                                                        children: child.name\n                                                    }, child.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${isActiveLink(item.href) ? 'text-primary-600 bg-primary-50' : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'}`,\n                                        children: [\n                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 37\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 21\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 pt-4 border-t border-gray-200 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onSearchClick,\n                                    className: \"w-full flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Buscar Servicios\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/login\",\n                                    className: \"w-full flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BuildingOffice2Icon_ChatBubbleLeftRightIcon_ChevronDownIcon_ClipboardDocumentListIcon_DocumentTextIcon_HomeIcon_InformationCircleIcon_MagnifyingGlassIcon_PhoneIcon_QuestionMarkCircleIcon_UserIcon_UsersIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Iniciar Sesi\\xf3n\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\MainNavigation.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/MainNavigation.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/PageLayout.tsx":
/*!******************************************!*\
  !*** ./components/layout/PageLayout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _MainNavigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MainNavigation */ \"(ssr)/./components/layout/MainNavigation.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./components/layout/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst PageLayout = ({ children, onSearchClick, onChatClick, className = '' })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-white flex flex-col ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MainNavigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                onSearchClick: onSearchClick,\n                onChatClick: onChatClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\PageLayout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\PageLayout.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\PageLayout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\components\\\\layout\\\\PageLayout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xheW91dC9QYWdlTGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHOEM7QUFDaEI7QUFTOUIsTUFBTUUsYUFBYSxDQUFDLEVBQ2xCQyxRQUFRLEVBQ1JDLGFBQWEsRUFDYkMsV0FBVyxFQUNYQyxZQUFZLEVBQUUsRUFDRTtJQUNoQixxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVyxDQUFDLG9DQUFvQyxFQUFFQSxXQUFXOzswQkFFaEUsOERBQUNOLHVEQUFjQTtnQkFDYkksZUFBZUE7Z0JBQ2ZDLGFBQWFBOzs7Ozs7MEJBSWYsOERBQUNHO2dCQUFLRixXQUFVOzBCQUNiSDs7Ozs7OzBCQUlILDhEQUFDRiwrQ0FBTUE7Ozs7Ozs7Ozs7O0FBR2I7QUFFQSxpRUFBZUMsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxKdWFuIFB1bGdhcmluXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoaWEtbmV4dFxcYXBwc1xcd2ViXFxjb21wb25lbnRzXFxsYXlvdXRcXFBhZ2VMYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IE1haW5OYXZpZ2F0aW9uIGZyb20gJy4vTWFpbk5hdmlnYXRpb24nO1xuaW1wb3J0IEZvb3RlciBmcm9tICcuL0Zvb3Rlcic7XG5cbmludGVyZmFjZSBQYWdlTGF5b3V0UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xuICBvblNlYXJjaENsaWNrPzogKCkgPT4gdm9pZDtcbiAgb25DaGF0Q2xpY2s/OiAoKSA9PiB2b2lkO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmNvbnN0IFBhZ2VMYXlvdXQgPSAoeyBcbiAgY2hpbGRyZW4sIFxuICBvblNlYXJjaENsaWNrLCBcbiAgb25DaGF0Q2xpY2ssIFxuICBjbGFzc05hbWUgPSAnJyBcbn06IFBhZ2VMYXlvdXRQcm9wcykgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgbWluLWgtc2NyZWVuIGJnLXdoaXRlIGZsZXggZmxleC1jb2wgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgIDxNYWluTmF2aWdhdGlvblxuICAgICAgICBvblNlYXJjaENsaWNrPXtvblNlYXJjaENsaWNrfVxuICAgICAgICBvbkNoYXRDbGljaz17b25DaGF0Q2xpY2t9XG4gICAgICAvPlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvbWFpbj5cblxuICAgICAgey8qIEZvb3RlciAqL31cbiAgICAgIDxGb290ZXIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFBhZ2VMYXlvdXQ7XG4iXSwibmFtZXMiOlsiTWFpbk5hdmlnYXRpb24iLCJGb290ZXIiLCJQYWdlTGF5b3V0IiwiY2hpbGRyZW4iLCJvblNlYXJjaENsaWNrIiwib25DaGF0Q2xpY2siLCJjbGFzc05hbWUiLCJkaXYiLCJtYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/PageLayout.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpreguntas-frecuentes%2Fpage&page=%2Fpreguntas-frecuentes%2Fpage&appPaths=%2Fpreguntas-frecuentes%2Fpage&pagePath=private-next-app-dir%2Fpreguntas-frecuentes%2Fpage.tsx&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();