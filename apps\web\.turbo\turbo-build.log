
> @chia/web@1.0.0 build
> next build

   ▲ Next.js 15.3.5

   Creating an optimized production build ...
Failed to compile.

./app/(auth)/login/login-client.tsx + 28 modules
Unexpected end of JSON input

./app/(auth)/verify-email/page.tsx + 3 modules
Unexpected end of JSON input

./app/page.tsx + 9 modules
Unexpected end of JSON input

./components/chat/ChatInterface.tsx + 1 modules
Unexpected end of JSON input

./components/layout/PageLayout.tsx + 6 modules
Unexpected end of JSON input


> Build failed because of webpack errors
npm error Lifecycle script `build` failed with error:
npm error code 1
npm error path C:\Users\<USER>\Documents\augment-projects\chia-next\apps\web
npm error workspace @chia/web@1.0.0
npm error location C:\Users\<USER>\Documents\augment-projects\chia-next\apps\web
npm error command failed
npm error command C:\WINDOWS\system32\cmd.exe /d /s /c next build
