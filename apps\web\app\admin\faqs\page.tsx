'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Plus, 
  PencilIcon,
  TrashIcon,
  EyeIcon,
  Filter,
  Building2,
  MessageCircleQuestion,
  Tag
} from 'lucide-react';
import Link from 'next/link';

interface FAQ {
  id: string;
  pregunta: string;
  respuesta: string;
  tema_nombre: string;
  dependencia_nombre: string;
  subdependencia_nombre: string;
  estado: string;
  created_at: string;
  updated_at: string;
}

interface ApiResponse {
  success: boolean;
  data: FAQ[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}

export default function AdminFAQsPage() {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTema, setSelectedTema] = useState<string>('');
  const [selectedEstado, setSelectedEstado] = useState<string>('');
  const [temas, setTemas] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchFaqs();
    fetchTemas();
  }, [currentPage, searchTerm, selectedTema, selectedEstado]);

  const fetchFaqs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (selectedTema) params.append('tema', selectedTema);
      if (selectedEstado) params.append('estado', selectedEstado);

      const response = await fetch(`/api/faqs?${params}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar FAQs');
      }

      const data: ApiResponse = await response.json();
      
      if (data.success) {
        setFaqs(data.data);
        setPagination(data.pagination);
      } else {
        throw new Error(data.error || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const fetchTemas = async () => {
    try {
      const response = await fetch('/api/faqs/temas');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTemas(data.data);
        }
      }
    } catch (err) {
      console.error('Error al cargar temas:', err);
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleTemaChange = (value: string) => {
    setSelectedTema(value === 'all' ? '' : value);
    setCurrentPage(1);
  };

  const handleEstadoChange = (value: string) => {
    setSelectedEstado(value === 'all' ? '' : value);
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedTema('');
    setSelectedEstado('');
    setCurrentPage(1);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('¿Estás seguro de que deseas eliminar esta FAQ?')) {
      return;
    }

    try {
      const response = await fetch(`/api/faqs/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchFaqs(); // Refresh the list
      } else {
        alert('Error al eliminar la FAQ');
      }
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      alert('Error al eliminar la FAQ');
    }
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (loading && faqs.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Cargando FAQs...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestión de FAQs</h1>
          <p className="text-gray-600">Administra las preguntas frecuentes</p>
        </div>
        <Link href="/admin/faqs/nuevo">
          <Button className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            Nueva FAQ
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Buscar FAQs..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedTema || 'all'} onValueChange={handleTemaChange}>
              <SelectTrigger>
                <SelectValue placeholder="Todos los temas" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los temas</SelectItem>
                {temas.map((tema) => (
                  <SelectItem key={tema} value={tema}>
                    {tema}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedEstado || 'all'} onValueChange={handleEstadoChange}>
              <SelectTrigger>
                <SelectValue placeholder="Todos los estados" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="activo">Activo</SelectItem>
                <SelectItem value="inactivo">Inactivo</SelectItem>
                <SelectItem value="borrador">Borrador</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={clearFilters}>
              <Filter className="h-4 w-4 mr-2" />
              Limpiar Filtros
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Mostrando {faqs.length} de {pagination.total} FAQs
        </p>
        <div className="text-sm text-gray-500">
          Página {pagination.page} de {pagination.totalPages}
        </div>
      </div>

      {/* FAQs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {faqs.map((faq) => (
          <Card key={faq.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base line-clamp-2 mb-2">
                    {faq.pregunta}
                  </CardTitle>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Tag className="h-3 w-3" />
                    <span>{faq.tema_nombre}</span>
                  </div>
                </div>
                <Badge 
                  variant={faq.estado === 'activo' ? 'default' : 'secondary'}
                  className={faq.estado === 'activo' ? 'bg-green-600' : ''}
                >
                  {faq.estado}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600 line-clamp-3">
                    {truncateText(faq.respuesta, 120)}
                  </p>
                </div>
                
                <div className="flex items-center text-xs text-gray-500">
                  <Building2 className="h-3 w-3 mr-1" />
                  <span className="truncate">
                    {faq.dependencia_nombre}
                    {faq.subdependencia_nombre && ` - ${faq.subdependencia_nombre}`}
                  </span>
                </div>
                
                <div className="flex items-center justify-between pt-2 border-t">
                  <div className="flex items-center space-x-2">
                    <Link href={`/faqs/${faq.id}`}>
                      <Button variant="ghost" size="sm">
                        <EyeIcon className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href={`/admin/faqs/${faq.id}/editar`}>
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDelete(faq.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="text-xs text-gray-400">
                    {new Date(faq.updated_at).toLocaleDateString('es-CO')}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1 || loading}
          >
            Anterior
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                  disabled={loading}
                  className={currentPage === page ? "bg-green-600 hover:bg-green-700" : ""}
                >
                  {page}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            onClick={() => setCurrentPage(Math.min(pagination.totalPages, currentPage + 1))}
            disabled={currentPage === pagination.totalPages || loading}
          >
            Siguiente
          </Button>
        </div>
      )}

      {faqs.length === 0 && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">❓</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No se encontraron FAQs
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedTema || selectedEstado 
                ? 'Intenta ajustar los filtros de búsqueda'
                : 'Comienza creando tu primera FAQ'
              }
            </p>
            {!searchTerm && !selectedTema && !selectedEstado && (
              <Link href="/admin/faqs/nuevo">
                <Button className="bg-green-600 hover:bg-green-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Crear Primera FAQ
                </Button>
              </Link>
            )}
            {(searchTerm || selectedTema || selectedEstado) && (
              <Button onClick={clearFilters}>
                Limpiar Filtros
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
