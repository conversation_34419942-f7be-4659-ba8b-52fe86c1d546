(()=>{var e={};e.id=8117,e.ids=[8117],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24768:(e,t,r)=>{"use strict";r.d(t,{y8:()=>i});var s=r(2492),a=r(62518);let o="https://hndowofzjzjoljnapokv.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(!o||!n)throw Error("Missing Supabase environment variables");let i=async()=>{let{cookies:e}=await r.e(5208).then(r.bind(r,65208)),t=e();return(0,a.createServerComponentClient)({cookies:()=>t})};(0,s.UU)(o,n)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59582:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var a=r(48106),o=r(48819),n=r(12050),i=r(4235),c=r(24768);async function d(e){try{let{searchParams:t}=new URL(e.url),r=t.get("search"),s=t.get("hasContent"),a=t.get("sortBy")||"nombre",o=t.get("order")||"asc",n=parseInt(t.get("limit")||"50"),d=parseInt(t.get("offset")||"0"),p=(0,c.y8)(),u=p.from("dependencias_view").select("*");if(r&&(u=u.ilike("nombre",`%${r}%`)),s)switch(s){case"tramites":u=u.gt("total_tramites",0);break;case"opas":u=u.gt("total_opas",0);break;case"faqs":u=u.gt("total_faqs",0);break;case"any":u=u.gt("total_contenido",0)}u=(u=u.order(a,{ascending:"asc"===o})).range(d,d+n-1);let{data:l,error:_,count:x}=await u;if(_)return console.error("Error fetching dependencias:",_),i.NextResponse.json({error:"Error al obtener dependencias"},{status:500});let{data:m}=await p.from("dependencias_view").select("total_subdependencias, total_tramites, total_opas, total_faqs, total_contenido"),g=m?.reduce((e,t)=>({total_dependencias:(e.total_dependencias||0)+1,total_subdependencias:e.total_subdependencias+t.total_subdependencias,total_tramites:e.total_tramites+t.total_tramites,total_opas:e.total_opas+t.total_opas,total_faqs:e.total_faqs+t.total_faqs,total_contenido:e.total_contenido+t.total_contenido}),{total_dependencias:0,total_subdependencias:0,total_tramites:0,total_opas:0,total_faqs:0,total_contenido:0});return i.NextResponse.json({success:!0,data:l,pagination:{limit:n,offset:d,total:x||0},stats:g})}catch(e){return console.error("Unexpected error in dependencias API:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function p(e){try{let{action:t}=await e.json();if("get_stats"===t){let e=(0,c.y8)(),{data:t,error:r}=await e.from("dependencias_view").select("*").order("total_contenido",{ascending:!1});if(r)throw r;let s={total_dependencias:t?.length||0,total_subdependencias:t?.reduce((e,t)=>e+t.total_subdependencias,0)||0,total_tramites:t?.reduce((e,t)=>e+t.total_tramites,0)||0,total_opas:t?.reduce((e,t)=>e+t.total_opas,0)||0,total_faqs:t?.reduce((e,t)=>e+t.total_faqs,0)||0,total_contenido:t?.reduce((e,t)=>e+t.total_contenido,0)||0,top_dependencias:t?.slice(0,5)||[],dependencias_sin_contenido:t?.filter(e=>0===e.total_contenido).length||0};return i.NextResponse.json({success:!0,data:s})}return i.NextResponse.json({error:"Acci\xf3n no v\xe1lida"},{status:400})}catch(e){return console.error("Error in dependencias POST:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/dependencias/route",pathname:"/api/dependencias",filename:"route",bundlePath:"app/api/dependencias/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\dependencias\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:_,serverHooks:x}=u;function m(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:_})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[191,2492,3744,2518],()=>r(59582));module.exports=s})();