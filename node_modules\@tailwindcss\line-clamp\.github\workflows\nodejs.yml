# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [12, 14, 16]

    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
      - name: Use cached node_modules
        id: cache
        uses: actions/cache@v2
        with:
          path: node_modules
          key: nodeModules-${{ hashFiles('**/package-lock.json') }}-${{ matrix.node-version }}
          restore-keys: |
            nodeModules-
      - name: Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: npm install
        env:
          CI: true
      - run: npm test
        env:
          CI: true
