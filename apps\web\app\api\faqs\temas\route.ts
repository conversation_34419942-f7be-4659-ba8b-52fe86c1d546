import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Obtener temas únicos de FAQs a través de la tabla temas
    const { data, error } = await supabase
      .from('temas')
      .select('nombre')
      .order('nombre');

    if (error) {
      console.error('Error fetching FAQ temas:', error);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Error al obtener temas de FAQs',
          details: error.message 
        },
        { status: 500 }
      );
    }

    // Extraer nombres únicos de temas
    const temas = data.map(item => item.nombre).filter(Boolean);

    return NextResponse.json({
      success: true,
      data: temas
    });

  } catch (error) {
    console.error('Unexpected error in FAQ temas API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
