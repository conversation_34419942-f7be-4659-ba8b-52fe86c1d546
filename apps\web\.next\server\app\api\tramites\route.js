(()=>{var e={};e.id=9881,e.ids=[9881],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18872:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>u,POST:()=>p});var i=t(48106),o=t(48819),a=t(12050),n=t(4235),c=t(73474);async function u(e){try{let r=(0,c.y8)(),{searchParams:t}=new URL(e.url),s=parseInt(t.get("limit")||"20"),i=parseInt(t.get("offset")||"0"),o=t.get("categoria"),a=t.get("dependencia"),u=t.get("search"),p=r.from("tramites_view").select("*").order("nombre").range(i,i+s-1);o&&(p=p.eq("categoria",o)),a&&(p=p.eq("dependencia_id",a)),u&&(p=p.or(`nombre.ilike.%${u}%,descripcion.ilike.%${u}%`));let{data:d,error:l,count:m}=await p;if(l)return console.error("Error fetching tramites:",l),n.NextResponse.json({error:"Failed to fetch government procedures",details:l.message},{status:500});let g=d?.map(e=>({id:e.id,nombre:e.nombre,descripcion:e.descripcion||"",categoria:e.categoria||"General",tiempoRespuesta:e.tiempo_estimado||"No especificado",tienePago:e.costo?"S\xed":"No",costoDetalle:e.costo,modalidad:e.modalidad||"Presencial",requisitos:e.requisitos||[],documentosRequeridos:[],urlSuit:"",urlGovco:"",popularidad:0,satisfaccion:0,dependencia:{id:e.dependencia_id,codigo:e.dependencia_codigo,nombre:e.dependencia_nombre,sigla:e.dependencia_sigla}}))||[];return n.NextResponse.json({success:!0,data:g,pagination:{limit:s,offset:i,total:m||g.length},filters:{categoria:o,dependencia:a,search:u}})}catch(e){return console.error("Unexpected error in tramites API:",e),n.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){try{let r=await e.json(),{action:t}=r,s=(0,c.y8)();if("get_categories"===t){let{data:e,error:r}=await s.schema("ingestion").from("tramites").select("categoria").eq("activo",!0).not("categoria","is",null);if(r)throw r;let t=[...new Set(e?.map(e=>e.categoria))].filter(Boolean);return n.NextResponse.json({success:!0,data:t})}if(!t){let{data:{user:e},error:t}=await s.auth.getUser();if(t||!e)return n.NextResponse.json({error:"No autorizado"},{status:401});let{data:i}=await s.rpc("check_user_permission",{p_user_id:e.id,p_action:"INSERT",p_table_name:"tramites"});if(!i)return n.NextResponse.json({error:"Sin permisos para crear tr\xe1mites"},{status:403});let{nombre:o,descripcion:a,categoria:c,requisitos:u,documentos_requeridos:p,tiempo_estimado:d,costo:l,modalidad:m,subdependencia_id:g,metadata:x}=r;if(!o||!g)return n.NextResponse.json({error:"Nombre y subdependencia son requeridos"},{status:400});let{data:f,error:h}=await s.schema("ingestion").from("tramites").insert({nombre:o,descripcion:a,categoria:c,requisitos:u,documentos_requeridos:p,tiempo_estimado:d,costo:l,modalidad:m,subdependencia_id:g,metadata:x,activo:!0,fuente_original:"portal_admin"}).select().single();if(h)return console.error("Error creating tramite:",h),n.NextResponse.json({error:"Error al crear tr\xe1mite"},{status:500});return n.NextResponse.json({success:!0,data:f})}return n.NextResponse.json({error:"Acci\xf3n no v\xe1lida"},{status:400})}catch(e){return console.error("Error in tramites POST:",e),n.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/tramites/route",pathname:"/api/tramites",filename:"route",bundlePath:"app/api/tramites/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\tramites\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:g}=d;function x(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,r,t)=>{"use strict";t.d(r,{y8:()=>a});var s=t(2492);let i="https://hndowofzjzjoljnapokv.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(process.env.SUPABASE_SERVICE_ROLE_KEY,!i||!o)throw Error("Missing Supabase environment variables");function a(){return(0,s.UU)(i,o)}a()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744],()=>t(18872));module.exports=s})();