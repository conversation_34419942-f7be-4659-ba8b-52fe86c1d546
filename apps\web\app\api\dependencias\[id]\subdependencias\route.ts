import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const dependenciaId = params.id;

    if (!dependenciaId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'ID de dependencia requerido' 
        },
        { status: 400 }
      );
    }

    // Obtener subdependencias de la dependencia específica
    const { data, error } = await supabase
      .from('subdependencias')
      .select('id, nombre, descripcion')
      .eq('dependencia_id', dependenciaId)
      .order('nombre');

    if (error) {
      console.error('Error fetching subdependencias:', error);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Error al obtener subdependencias',
          details: error.message 
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Unexpected error in subdependencias API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
