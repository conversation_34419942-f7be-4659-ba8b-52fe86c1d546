'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { MainNavigation } from '@/components/layout/MainNavigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeft, 
  FileText, 
  Building2, 
  Clock, 
  DollarSign, 
  CheckCircle,
  AlertCircle,
  ChevronRight,
  ExternalLink,
  Download
} from 'lucide-react';
import Link from 'next/link';

interface Tramite {
  id: string;
  nombre: string;
  descripcion: string;
  categoria: string;
  dependencia_id: string;
  dependencia_nombre: string;
  subdependencia_id: string;
  subdependencia_nombre: string;
  tiempo_estimado: string;
  costo: string;
  requisitos: string[];
  documentos_requeridos: string[];
  modalidad: string;
  estado: string;
  observaciones: string;
  normativa: string;
  created_at: string;
  updated_at: string;
}

export default function TramiteDetailPage() {
  const params = useParams();
  const tramiteId = params.id as string;
  
  const [tramite, setTramite] = useState<Tramite | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (tramiteId) {
      fetchTramiteDetail();
    }
  }, [tramiteId]);

  const fetchTramiteDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/tramites/${tramiteId}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar detalle del trámite');
      }

      const data = await response.json();
      
      if (data.success) {
        setTramite(data.data);
      } else {
        throw new Error(data.error || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando información del trámite...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !tramite) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar</h2>
              <p className="text-gray-600 mb-4">{error || 'Trámite no encontrado'}</p>
              <Link href="/tramites">
                <Button>
                  Volver a Trámites
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNavigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <Link href="/tramites" className="hover:text-blue-600">
            Trámites
          </Link>
          <ChevronRight className="h-4 w-4" />
          <Link href={`/dependencias/${tramite.dependencia_id}`} className="hover:text-blue-600">
            {tramite.dependencia_nombre}
          </Link>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-900 truncate">{tramite.nombre}</span>
        </nav>

        {/* Back Button */}
        <div className="mb-6">
          <Link href="/tramites">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver a Trámites
            </Button>
          </Link>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-green-100 rounded-lg">
              <FileText className="h-8 w-8 text-green-600" />
            </div>
            <div className="flex-1">
              <div className="flex items-start justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {tramite.nombre}
                  </h1>
                  <div className="flex items-center space-x-3 mb-4">
                    <Badge variant="secondary">{tramite.categoria}</Badge>
                    <Badge 
                      variant={tramite.estado === 'activo' ? 'default' : 'secondary'}
                    >
                      {tramite.estado}
                    </Badge>
                  </div>
                </div>
              </div>
              {tramite.descripcion && (
                <p className="text-gray-600 text-lg">
                  {tramite.descripcion}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Información General */}
            <Card>
              <CardHeader>
                <CardTitle>Información General</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <Building2 className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Dependencia</p>
                      <p className="font-medium">{tramite.dependencia_nombre}</p>
                      {tramite.subdependencia_nombre && (
                        <p className="text-sm text-gray-500">{tramite.subdependencia_nombre}</p>
                      )}
                    </div>
                  </div>
                  
                  {tramite.tiempo_estimado && (
                    <div className="flex items-center space-x-3">
                      <Clock className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Tiempo Estimado</p>
                        <p className="font-medium">{tramite.tiempo_estimado}</p>
                      </div>
                    </div>
                  )}
                  
                  {tramite.costo && (
                    <div className="flex items-center space-x-3">
                      <DollarSign className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Costo</p>
                        <p className="font-medium">{tramite.costo}</p>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Modalidad</p>
                      <p className="font-medium">{tramite.modalidad}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Requisitos */}
            {tramite.requisitos && tramite.requisitos.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Requisitos</CardTitle>
                  <CardDescription>
                    Documentos y condiciones necesarias para realizar el trámite
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {tramite.requisitos.map((requisito, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 shrink-0" />
                        <span className="text-gray-700">{requisito}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Documentos Requeridos */}
            {tramite.documentos_requeridos && tramite.documentos_requeridos.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Documentos Requeridos</CardTitle>
                  <CardDescription>
                    Documentos que debe presentar para el trámite
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {tramite.documentos_requeridos.map((documento, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <FileText className="h-5 w-5 text-blue-600 mt-0.5 shrink-0" />
                        <span className="text-gray-700">{documento}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Observaciones */}
            {tramite.observaciones && (
              <Card>
                <CardHeader>
                  <CardTitle>Observaciones</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 shrink-0" />
                    <p className="text-gray-700">{tramite.observaciones}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Normativa */}
            {tramite.normativa && (
              <Card>
                <CardHeader>
                  <CardTitle>Marco Normativo</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{tramite.normativa}</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Acciones Rápidas */}
            <Card>
              <CardHeader>
                <CardTitle>Acciones</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" size="lg">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Iniciar Trámite
                </Button>
                
                <Button variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Descargar Formulario
                </Button>
                
                <Link href={`/dependencias/${tramite.dependencia_id}`}>
                  <Button variant="outline" className="w-full">
                    <Building2 className="h-4 w-4 mr-2" />
                    Ver Dependencia
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Información de Contacto */}
            <Card>
              <CardHeader>
                <CardTitle>¿Necesitas Ayuda?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-gray-600">
                  Para más información sobre este trámite, puedes contactar directamente a:
                </p>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium text-gray-900">{tramite.dependencia_nombre}</p>
                  {tramite.subdependencia_nombre && (
                    <p className="text-sm text-gray-600">{tramite.subdependencia_nombre}</p>
                  )}
                </div>
                <Link href="/contacto">
                  <Button variant="outline" size="sm" className="w-full">
                    Ver Información de Contacto
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Trámites Relacionados */}
            <Card>
              <CardHeader>
                <CardTitle>Trámites Relacionados</CardTitle>
              </CardHeader>
              <CardContent>
                <Link href={`/tramites?dependencia=${tramite.dependencia_id}`}>
                  <Button variant="outline" size="sm" className="w-full">
                    Ver Más Trámites de esta Dependencia
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
