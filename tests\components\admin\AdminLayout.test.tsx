import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter, usePathname } from 'next/navigation';
import AdminLayout from '../../../apps/web/app/admin/layout';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn()
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  );
});

const mockRouter = {
  push: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  replace: jest.fn()
};

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('AdminLayout', () => {
  beforeEach(() => {
    mockUseRouter.mockReturnValue(mockRouter);
    mockUsePathname.mockReturnValue('/admin');
    jest.clearAllMocks();
  });

  it('renders admin layout with sidebar navigation', () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    // Check if main navigation items are present
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Dependencias')).toBeInTheDocument();
    expect(screen.getByText('Trámites')).toBeInTheDocument();
    expect(screen.getByText('OPAs')).toBeInTheDocument();
    expect(screen.getByText('FAQs')).toBeInTheDocument();
    expect(screen.getByText('Usuarios')).toBeInTheDocument();
    expect(screen.getByText('Reportes')).toBeInTheDocument();
    expect(screen.getByText('Configuración')).toBeInTheDocument();
  });

  it('renders children content correctly', () => {
    render(
      <AdminLayout>
        <div data-testid="test-content">Test Content</div>
      </AdminLayout>
    );

    expect(screen.getByTestId('test-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    // Should show loading initially
    expect(screen.getByText('Verificando permisos...')).toBeInTheDocument();
  });

  it('shows user information after loading', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    // Should show user info
    expect(screen.getByText('Administrador')).toBeInTheDocument();
    expect(screen.getByText('admin - Todas')).toBeInTheDocument();
  });

  it('handles mobile sidebar toggle', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    // Find and click mobile menu button
    const mobileMenuButton = screen.getAllByRole('button').find(
      button => button.querySelector('svg') // Button with hamburger icon
    );

    if (mobileMenuButton) {
      fireEvent.click(mobileMenuButton);
      // Sidebar should be visible on mobile
      // This would require checking CSS classes or aria attributes
    }
  });

  it('highlights active navigation item correctly', () => {
    mockUsePathname.mockReturnValue('/admin/tramites');
    
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    // The Trámites link should be highlighted
    const tramitesLink = screen.getByRole('link', { name: /trámites/i });
    expect(tramitesLink).toHaveClass('bg-blue-50', 'text-blue-700');
  });

  it('handles logout functionality', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    const logoutButton = screen.getByText('Cerrar Sesión');
    fireEvent.click(logoutButton);

    expect(mockRouter.push).toHaveBeenCalledWith('/');
  });

  it('provides link to public site', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    const publicSiteLink = screen.getByText('Ver Sitio Público');
    expect(publicSiteLink).toHaveAttribute('href', '/');
  });

  it('renders CHIA logo and branding', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    expect(screen.getByText('CHIA')).toBeInTheDocument();
    expect(screen.getByText('Administración')).toBeInTheDocument();
  });

  it('shows navigation descriptions', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Resumen general del sistema')).toBeInTheDocument();
    expect(screen.getByText('Administrar trámites municipales')).toBeInTheDocument();
    expect(screen.getByText('Gestión de procedimientos administrativos')).toBeInTheDocument();
  });

  it('handles different pathname scenarios', () => {
    // Test dashboard active state
    mockUsePathname.mockReturnValue('/admin');
    const { rerender } = render(
      <AdminLayout>
        <div>Dashboard Content</div>
      </AdminLayout>
    );

    let dashboardLink = screen.getByRole('link', { name: /dashboard/i });
    expect(dashboardLink).toHaveClass('bg-blue-50');

    // Test nested path active state
    mockUsePathname.mockReturnValue('/admin/tramites/nuevo');
    rerender(
      <AdminLayout>
        <div>New Tramite Content</div>
      </AdminLayout>
    );

    const tramitesLink = screen.getByRole('link', { name: /trámites/i });
    expect(tramitesLink).toHaveClass('bg-blue-50');
  });

  it('closes mobile sidebar when navigation item is clicked', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    // This test would require more complex setup to test mobile sidebar state
    // For now, we verify the navigation links are clickable
    const tramitesLink = screen.getByRole('link', { name: /trámites/i });
    expect(tramitesLink).toHaveAttribute('href', '/admin/tramites');
  });
});

describe('AdminLayout Accessibility', () => {
  beforeEach(() => {
    mockUseRouter.mockReturnValue(mockRouter);
    mockUsePathname.mockReturnValue('/admin');
  });

  it('has proper ARIA labels and roles', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    // Check for navigation role
    const navigation = screen.getByRole('navigation', { hidden: true });
    expect(navigation).toBeInTheDocument();

    // Check for main content area
    const main = screen.getByRole('main');
    expect(main).toBeInTheDocument();
  });

  it('supports keyboard navigation', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    // All navigation links should be focusable
    const links = screen.getAllByRole('link');
    links.forEach(link => {
      expect(link).toHaveAttribute('href');
    });
  });
});

describe('AdminLayout Responsive Behavior', () => {
  beforeEach(() => {
    mockUseRouter.mockReturnValue(mockRouter);
    mockUsePathname.mockReturnValue('/admin');
  });

  it('adapts to different screen sizes', async () => {
    // Mock window.matchMedia for responsive tests
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query.includes('lg'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    await waitFor(() => {
      expect(screen.queryByText('Verificando permisos...')).not.toBeInTheDocument();
    });

    // On larger screens, sidebar should be visible
    // On mobile, sidebar should be hidden by default
    // This would require more sophisticated testing setup
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });
});
