import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const categoria = searchParams.get('categoria');
    const dependencia = searchParams.get('dependencia');
    const search = searchParams.get('search');

    let query = supabase
      .from('tramites_view')
      .select('*')
      .order('nombre')
      .range(offset, offset + limit - 1);

    // Apply filters
    if (categoria) {
      query = query.eq('categoria', categoria);
    }

    if (dependencia) {
      query = query.eq('dependencia_id', dependencia);
    }

    // Apply search filter using ilike for now (since we don't have full-text search in view)
    if (search) {
      query = query.or(`nombre.ilike.%${search}%,descripcion.ilike.%${search}%`);
    }

    const { data: tramites, error, count } = await query;

    if (error) {
      console.error('Error fetching tramites:', error);
      return NextResponse.json(
        { error: 'Failed to fetch government procedures', details: error.message },
        { status: 500 }
      );
    }

    // Transform data to match frontend expectations
    const transformedTramites = tramites?.map(tramite => ({
      id: tramite.id,
      nombre: tramite.nombre,
      descripcion: tramite.descripcion || '',
      categoria: tramite.categoria || 'General',
      tiempoRespuesta: tramite.tiempo_estimado || 'No especificado',
      tienePago: tramite.costo ? 'Sí' : 'No',
      costoDetalle: tramite.costo,
      modalidad: tramite.modalidad || 'Presencial',
      requisitos: tramite.requisitos || [],
      documentosRequeridos: [],
      urlSuit: '',
      urlGovco: '',
      popularidad: 0,
      satisfaccion: 0,
      dependencia: {
        id: tramite.dependencia_id,
        codigo: tramite.dependencia_codigo,
        nombre: tramite.dependencia_nombre,
        sigla: tramite.dependencia_sigla
      }
    })) || [];

    return NextResponse.json({
      success: true,
      data: transformedTramites,
      pagination: {
        limit,
        offset,
        total: count || transformedTramites.length
      },
      filters: {
        categoria,
        dependencia,
        search
      }
    });

  } catch (error) {
    console.error('Unexpected error in tramites API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tramites
 * Crea un nuevo trámite o ejecuta acciones específicas
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    const supabase = createServerSupabase();

    // Acción para obtener categorías
    if (action === 'get_categories') {
      const { data: categories, error } = await supabase
        .schema('ingestion')
        .from('tramites')
        .select('categoria')
        .eq('activo', true)
        .not('categoria', 'is', null);

      if (error) {
        throw error;
      }

      const uniqueCategories = [...new Set(categories?.map(t => t.categoria))].filter(Boolean);

      return NextResponse.json({
        success: true,
        data: uniqueCategories
      });
    }

    // Crear nuevo trámite
    if (!action) {
      // Verificar autenticación
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return NextResponse.json(
          { error: 'No autorizado' },
          { status: 401 }
        );
      }

      // Verificar permisos
      const { data: hasPermission } = await supabase
        .rpc('check_user_permission', {
          p_user_id: user.id,
          p_action: 'INSERT',
          p_table_name: 'tramites'
        });

      if (!hasPermission) {
        return NextResponse.json(
          { error: 'Sin permisos para crear trámites' },
          { status: 403 }
        );
      }

      const {
        nombre,
        descripcion,
        categoria,
        requisitos,
        documentos_requeridos,
        tiempo_estimado,
        costo,
        modalidad,
        subdependencia_id,
        metadata
      } = body;

      // Validar campos requeridos
      if (!nombre || !subdependencia_id) {
        return NextResponse.json(
          { error: 'Nombre y subdependencia son requeridos' },
          { status: 400 }
        );
      }

      // Crear trámite
      const { data: newTramite, error: createError } = await supabase
        .schema('ingestion')
        .from('tramites')
        .insert({
          nombre,
          descripcion,
          categoria,
          requisitos,
          documentos_requeridos,
          tiempo_estimado,
          costo,
          modalidad,
          subdependencia_id,
          metadata,
          activo: true,
          fuente_original: 'portal_admin'
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating tramite:', createError);
        return NextResponse.json(
          { error: 'Error al crear trámite' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: newTramite
      });
    }

    return NextResponse.json(
      { error: 'Acción no válida' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in tramites POST:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
