import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const categoria = searchParams.get('categoria');
    const dependencia = searchParams.get('dependencia');
    const search = searchParams.get('search');

    let query = supabase
      .from('tramites_view')
      .select('*')
      .order('nombre')
      .range(offset, offset + limit - 1);

    // Apply filters
    if (categoria) {
      query = query.eq('categoria', categoria);
    }

    if (dependencia) {
      query = query.eq('dependencia_id', dependencia);
    }

    // Apply search filter using ilike for now (since we don't have full-text search in view)
    if (search) {
      query = query.or(`nombre.ilike.%${search}%,descripcion.ilike.%${search}%`);
    }

    const { data: tramites, error, count } = await query;

    if (error) {
      console.error('Error fetching tramites:', error);
      return NextResponse.json(
        { error: 'Failed to fetch government procedures', details: error.message },
        { status: 500 }
      );
    }

    // Transform data to match frontend expectations
    const transformedTramites = tramites?.map(tramite => ({
      id: tramite.id,
      nombre: tramite.nombre,
      descripcion: tramite.descripcion || '',
      categoria: tramite.categoria || 'General',
      tiempoRespuesta: tramite.tiempo_estimado || 'No especificado',
      tienePago: tramite.costo ? 'Sí' : 'No',
      costoDetalle: tramite.costo,
      modalidad: tramite.modalidad || 'Presencial',
      requisitos: tramite.requisitos || [],
      documentosRequeridos: [],
      urlSuit: '',
      urlGovco: '',
      popularidad: 0,
      satisfaccion: 0,
      dependencia: {
        id: tramite.dependencia_id,
        codigo: tramite.dependencia_codigo,
        nombre: tramite.dependencia_nombre,
        sigla: tramite.dependencia_sigla
      }
    })) || [];

    return NextResponse.json({
      success: true,
      data: transformedTramites,
      pagination: {
        limit,
        offset,
        total: count || transformedTramites.length
      },
      filters: {
        categoria,
        dependencia,
        search
      }
    });

  } catch (error) {
    console.error('Unexpected error in tramites API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// Get categories for filtering
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'get_categories') {
      const supabase = createServerSupabase();
      
      const { data: categories, error } = await supabase
        .schema('ingestion')
        .from('tramites')
        .select('categoria')
        .eq('activo', true)
        .not('categoria', 'is', null);

      if (error) {
        throw error;
      }

      const uniqueCategories = [...new Set(categories?.map(t => t.categoria))].filter(Boolean);
      
      return NextResponse.json({
        success: true,
        data: uniqueCategories
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in tramites POST:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
