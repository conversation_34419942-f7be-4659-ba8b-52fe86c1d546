(()=>{var e={};e.id=9242,e.ids=[9242],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6386:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(48106),a=t(48819),i=t(12050),n=t(4235);let u=(0,t(2492).UU)("https://hndowofzjzjoljnapokv.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function p(e){try{let{data:e,error:r}=await u.from("opas").select("categoria").not("categoria","is",null).not("categoria","eq","").order("categoria");if(r)return console.error("Error fetching OPAs categories:",r),n.NextResponse.json({success:!1,error:"Error al obtener categor\xedas de OPAs",details:r.message},{status:500});let t=[...new Set(e.map(e=>e.categoria))].filter(Boolean);return n.NextResponse.json({success:!0,data:t})}catch(e){return console.error("Unexpected error in OPAs categories API:",e),n.NextResponse.json({success:!1,error:"Error interno del servidor",details:e instanceof Error?e.message:"Error desconocido"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/opas/categorias/route",pathname:"/api/opas/categorias",filename:"route",bundlePath:"app/api/opas/categorias/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\opas\\categorias\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function g(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744],()=>t(6386));module.exports=s})();