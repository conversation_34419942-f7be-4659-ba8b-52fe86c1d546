"use strict";exports.id=680,exports.ids=[680],exports.modules={25487:(e,s,t)=>{t.d(s,{default:()=>m});var a=t(13486),r=t(60159),l=t(49989),n=t.n(l),i=t(54575),d=t(85480),c=t(61817),o=t(36151);function m({title:e="Preguntas Frecuentes",subtitle:s="Encuentra respuestas r\xe1pidas a las consultas m\xe1s comunes",limit:t=6,showViewAll:l=!0,className:m=""}){let[x,u]=(0,r.useState)([]),[h,p]=(0,r.useState)(!0),[g,j]=(0,r.useState)(null),[b,f]=(0,r.useState)(null),N=async()=>{try{p(!0);let e=await fetch(`/api/faqs?limit=${t}`);if(!e.ok)throw Error("Error al cargar las preguntas frecuentes");let s=await e.json();u(s.data)}catch(e){j(e instanceof Error?e.message:"Error desconocido")}finally{p(!1)}},y=e=>{f(b===e?null:e)};return h?(0,a.jsx)("div",{className:`bg-white rounded-xl shadow-md p-8 ${m}`,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Cargando preguntas frecuentes..."})]})}):g?(0,a.jsx)("div",{className:`bg-white rounded-xl shadow-md p-8 ${m}`,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(i.A,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:g}),(0,a.jsx)("button",{onClick:N,className:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"Reintentar"})]})}):(0,a.jsxs)("div",{className:`bg-white rounded-xl shadow-md p-8 ${m}`,children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(i.A,{className:"h-12 w-12 text-primary-600 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:e}),(0,a.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:s})]}),0===x.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(i.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No hay preguntas frecuentes disponibles"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Vuelve m\xe1s tarde para ver las preguntas m\xe1s comunes."})]}):(0,a.jsx)("div",{className:"space-y-4",children:x.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,a.jsx)("button",{onClick:()=>y(e.id),className:"w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.pregunta}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,a.jsx)("span",{className:"bg-primary-100 text-primary-800 px-2 py-1 rounded-full",children:e.tema}),(0,a.jsx)("span",{children:e.dependencia.nombre})]})]}),(0,a.jsx)("div",{className:"ml-4",children:b===e.id?(0,a.jsx)(d.A,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(c.A,{className:"h-5 w-5 text-gray-400"})})]})}),b===e.id&&(0,a.jsx)("div",{className:"px-6 pb-6 border-t border-gray-100",children:(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)("div",{className:"prose max-w-none text-gray-700",children:e.respuesta.split("\n").map((e,s)=>(0,a.jsx)("p",{className:"mb-3 last:mb-0",children:e},s))})})})]},e.id))}),l&&x.length>0&&(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)(n(),{href:"/preguntas-frecuentes",className:"inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium transition-colors",children:["Ver todas las preguntas frecuentes",(0,a.jsx)(o.A,{className:"h-4 w-4"})]})})]})}},32514:!1,36151:(e,s,t)=>{t.d(s,{A:()=>r});var a=t(60159);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))})},85480:(e,s,t)=>{t.d(s,{A:()=>r});var a=t(60159);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))})}};