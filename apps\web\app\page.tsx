'use client';

import { useState } from 'react';
import PageLayout from '@/components/layout/PageLayout';
import HeroSection from '@/components/landing/HeroSection';
import FeaturedServices from '@/components/landing/FeaturedServices';
import ContactInfo from '@/components/landing/ContactInfo';
import ChatWidget from '@/components/landing/ChatWidget';
import FAQSection from '@/components/faq/FAQSection';

export default function HomePage() {
  const [isChatOpen, setIsChatOpen] = useState(false);

  const handleSearchClick = () => {
    // In a real implementation, this would open a search modal or navigate to search page
    console.log('Search clicked');
  };

  const handleChatClick = () => {
    setIsChatOpen(true);
  };

  const handleServiceSelect = (serviceId: string) => {
    // In a real implementation, this would navigate to the specific service
    console.log('Service selected:', serviceId);
  };

  return (
    <PageLayout
      onSearchClick={handleSearchClick}
      onChatClick={handleChatClick}
    >
      {/* Hero Section */}
      <HeroSection
        onSearchClick={handleSearchClick}
        onChatClick={handleChatClick}
      />

      {/* Featured Services */}
      <FeaturedServices onServiceSelect={handleServiceSelect} />

      {/* FAQ Section */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FAQSection limit={4} />
        </div>
      </div>

      {/* Contact Information */}
      <ContactInfo />

      {/* Chat Widget */}
      <ChatWidget
        isOpen={isChatOpen}
        onToggle={() => setIsChatOpen(!isChatOpen)}
      />
    </PageLayout>
  );
}
