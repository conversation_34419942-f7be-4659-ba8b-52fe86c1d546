'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { MainNavigation } from '@/components/layout/MainNavigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeft, 
  Users, 
  Building2, 
  FileText, 
  Calendar,
  User,
  Target,
  Scope,
  ChevronRight,
  Download,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';

interface OPA {
  id: string;
  nombre: string;
  descripcion: string;
  categoria: string;
  dependencia_id: string;
  dependencia_nombre: string;
  subdependencia_id: string;
  subdependencia_nombre: string;
  objetivo: string;
  alcance: string;
  responsable: string;
  procedimiento: string[];
  documentos_asociados: string[];
  normativa: string;
  estado: string;
  version: string;
  fecha_aprobacion: string;
  observaciones: string;
  created_at: string;
  updated_at: string;
}

export default function OPADetailPage() {
  const params = useParams();
  const opaId = params.id as string;
  
  const [opa, setOpa] = useState<OPA | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (opaId) {
      fetchOPADetail();
    }
  }, [opaId]);

  const fetchOPADetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/opas/${opaId}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar detalle del OPA');
      }

      const data = await response.json();
      
      if (data.success) {
        setOpa(data.data);
      } else {
        throw new Error(data.error || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'No especificada';
    return new Date(dateString).toLocaleDateString('es-CO', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando información del OPA...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !opa) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar</h2>
              <p className="text-gray-600 mb-4">{error || 'OPA no encontrado'}</p>
              <Link href="/opas">
                <Button>
                  Volver a OPAs
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNavigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <Link href="/opas" className="hover:text-blue-600">
            OPAs
          </Link>
          <ChevronRight className="h-4 w-4" />
          <Link href={`/dependencias/${opa.dependencia_id}`} className="hover:text-blue-600">
            {opa.dependencia_nombre}
          </Link>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-900 truncate">{opa.nombre}</span>
        </nav>

        {/* Back Button */}
        <div className="mb-6">
          <Link href="/opas">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver a OPAs
            </Button>
          </Link>
        </div>

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Users className="h-8 w-8 text-purple-600" />
            </div>
            <div className="flex-1">
              <div className="flex items-start justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {opa.nombre}
                  </h1>
                  <div className="flex items-center space-x-3 mb-4">
                    <Badge variant="secondary">{opa.categoria}</Badge>
                    <Badge 
                      variant={opa.estado === 'activo' ? 'default' : 'secondary'}
                    >
                      {opa.estado}
                    </Badge>
                    {opa.version && (
                      <Badge variant="outline">
                        Versión {opa.version}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              {opa.descripcion && (
                <p className="text-gray-600 text-lg">
                  {opa.descripcion}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Información General */}
            <Card>
              <CardHeader>
                <CardTitle>Información General</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <Building2 className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Dependencia</p>
                      <p className="font-medium">{opa.dependencia_nombre}</p>
                      {opa.subdependencia_nombre && (
                        <p className="text-sm text-gray-500">{opa.subdependencia_nombre}</p>
                      )}
                    </div>
                  </div>
                  
                  {opa.responsable && (
                    <div className="flex items-center space-x-3">
                      <User className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Responsable</p>
                        <p className="font-medium">{opa.responsable}</p>
                      </div>
                    </div>
                  )}
                  
                  {opa.fecha_aprobacion && (
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Fecha de Aprobación</p>
                        <p className="font-medium">{formatDate(opa.fecha_aprobacion)}</p>
                      </div>
                    </div>
                  )}
                  
                  {opa.version && (
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">Versión</p>
                        <p className="font-medium">{opa.version}</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Objetivo */}
            {opa.objetivo && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Target className="h-5 w-5" />
                    <span>Objetivo</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{opa.objetivo}</p>
                </CardContent>
              </Card>
            )}

            {/* Alcance */}
            {opa.alcance && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Scope className="h-5 w-5" />
                    <span>Alcance</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{opa.alcance}</p>
                </CardContent>
              </Card>
            )}

            {/* Procedimiento */}
            {opa.procedimiento && opa.procedimiento.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Procedimiento</CardTitle>
                  <CardDescription>
                    Pasos a seguir para ejecutar este procedimiento administrativo
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ol className="space-y-3">
                    {opa.procedimiento.map((paso, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <span className="text-gray-700">{paso}</span>
                      </li>
                    ))}
                  </ol>
                </CardContent>
              </Card>
            )}

            {/* Documentos Asociados */}
            {opa.documentos_asociados && opa.documentos_asociados.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Documentos Asociados</CardTitle>
                  <CardDescription>
                    Documentos relacionados con este procedimiento
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {opa.documentos_asociados.map((documento, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <FileText className="h-5 w-5 text-blue-600 mt-0.5 shrink-0" />
                        <span className="text-gray-700">{documento}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Observaciones */}
            {opa.observaciones && (
              <Card>
                <CardHeader>
                  <CardTitle>Observaciones</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 shrink-0" />
                    <p className="text-gray-700">{opa.observaciones}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Normativa */}
            {opa.normativa && (
              <Card>
                <CardHeader>
                  <CardTitle>Marco Normativo</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{opa.normativa}</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Acciones Rápidas */}
            <Card>
              <CardHeader>
                <CardTitle>Acciones</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" size="lg">
                  <Download className="h-4 w-4 mr-2" />
                  Descargar Procedimiento
                </Button>
                
                <Link href={`/dependencias/${opa.dependencia_id}`}>
                  <Button variant="outline" className="w-full">
                    <Building2 className="h-4 w-4 mr-2" />
                    Ver Dependencia
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Información de Contacto */}
            <Card>
              <CardHeader>
                <CardTitle>¿Necesitas Más Información?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-gray-600">
                  Para consultas sobre este procedimiento, contacta a:
                </p>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium text-gray-900">{opa.dependencia_nombre}</p>
                  {opa.subdependencia_nombre && (
                    <p className="text-sm text-gray-600">{opa.subdependencia_nombre}</p>
                  )}
                  {opa.responsable && (
                    <p className="text-sm text-gray-600 mt-1">
                      <span className="font-medium">Responsable:</span> {opa.responsable}
                    </p>
                  )}
                </div>
                <Link href="/contacto">
                  <Button variant="outline" size="sm" className="w-full">
                    Ver Información de Contacto
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* OPAs Relacionados */}
            <Card>
              <CardHeader>
                <CardTitle>OPAs Relacionados</CardTitle>
              </CardHeader>
              <CardContent>
                <Link href={`/opas?dependencia=${opa.dependencia_id}`}>
                  <Button variant="outline" size="sm" className="w-full">
                    Ver Más OPAs de esta Dependencia
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
