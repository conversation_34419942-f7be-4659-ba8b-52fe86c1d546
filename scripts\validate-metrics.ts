#!/usr/bin/env tsx

/**
 * Script de Validación de Métricas del Sistema CHIA
 * 
 * Este script valida la consistencia de las métricas y conteos
 * en todo el sistema jerárquico de dependencias.
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface ValidationResult {
  entity: string;
  expected: number;
  actual: number;
  status: 'PASS' | 'FAIL';
  error?: string;
}

interface MetricsReport {
  timestamp: string;
  totalValidations: number;
  passed: number;
  failed: number;
  results: ValidationResult[];
  summary: {
    dependencias: number;
    subdependencias: number;
    tramites: number;
    opas: number;
    faqs: number;
  };
}

class MetricsValidator {
  private results: ValidationResult[] = [];

  async validateDependencyMetrics(): Promise<void> {
    console.log('🔍 Validando métricas de dependencias...');

    try {
      // Obtener todas las dependencias con sus métricas
      const { data: dependencias, error: depError } = await supabase
        .from('dependencias_view')
        .select('*');

      if (depError) {
        throw new Error(`Error al obtener dependencias: ${depError.message}`);
      }

      for (const dep of dependencias || []) {
        await this.validateDependencyTramites(dep);
        await this.validateDependencyOpas(dep);
        await this.validateDependencyFaqs(dep);
      }

    } catch (error) {
      console.error('❌ Error validando métricas de dependencias:', error);
    }
  }

  private async validateDependencyTramites(dependencia: any): Promise<void> {
    try {
      const { count, error } = await supabase
        .from('tramites')
        .select('*', { count: 'exact', head: true })
        .eq('dependencia_id', dependencia.id);

      if (error) {
        this.results.push({
          entity: `Dependencia ${dependencia.nombre} - Trámites`,
          expected: dependencia.total_tramites,
          actual: 0,
          status: 'FAIL',
          error: error.message
        });
        return;
      }

      const actualCount = count || 0;
      const expectedCount = dependencia.total_tramites || 0;

      this.results.push({
        entity: `Dependencia ${dependencia.nombre} - Trámites`,
        expected: expectedCount,
        actual: actualCount,
        status: actualCount === expectedCount ? 'PASS' : 'FAIL'
      });

    } catch (error) {
      this.results.push({
        entity: `Dependencia ${dependencia.nombre} - Trámites`,
        expected: dependencia.total_tramites,
        actual: 0,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  private async validateDependencyOpas(dependencia: any): Promise<void> {
    try {
      const { count, error } = await supabase
        .from('opas')
        .select('*', { count: 'exact', head: true })
        .eq('dependencia_id', dependencia.id);

      if (error) {
        this.results.push({
          entity: `Dependencia ${dependencia.nombre} - OPAs`,
          expected: dependencia.total_opas,
          actual: 0,
          status: 'FAIL',
          error: error.message
        });
        return;
      }

      const actualCount = count || 0;
      const expectedCount = dependencia.total_opas || 0;

      this.results.push({
        entity: `Dependencia ${dependencia.nombre} - OPAs`,
        expected: expectedCount,
        actual: actualCount,
        status: actualCount === expectedCount ? 'PASS' : 'FAIL'
      });

    } catch (error) {
      this.results.push({
        entity: `Dependencia ${dependencia.nombre} - OPAs`,
        expected: dependencia.total_opas,
        actual: 0,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  private async validateDependencyFaqs(dependencia: any): Promise<void> {
    try {
      const { count, error } = await supabase
        .from('faqs')
        .select('*', { count: 'exact', head: true })
        .eq('dependencia_id', dependencia.id);

      if (error) {
        this.results.push({
          entity: `Dependencia ${dependencia.nombre} - FAQs`,
          expected: dependencia.total_faqs,
          actual: 0,
          status: 'FAIL',
          error: error.message
        });
        return;
      }

      const actualCount = count || 0;
      const expectedCount = dependencia.total_faqs || 0;

      this.results.push({
        entity: `Dependencia ${dependencia.nombre} - FAQs`,
        expected: expectedCount,
        actual: actualCount,
        status: actualCount === expectedCount ? 'PASS' : 'FAIL'
      });

    } catch (error) {
      this.results.push({
        entity: `Dependencia ${dependencia.nombre} - FAQs`,
        expected: dependencia.total_faqs,
        actual: 0,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  async validateSubdependencyMetrics(): Promise<void> {
    console.log('🔍 Validando métricas de subdependencias...');

    try {
      const { data: subdependencias, error } = await supabase
        .from('subdependencias_detail_view')
        .select('*');

      if (error) {
        throw new Error(`Error al obtener subdependencias: ${error.message}`);
      }

      for (const subdep of subdependencias || []) {
        await this.validateSubdependencyTramites(subdep);
        await this.validateSubdependencyOpas(subdep);
        await this.validateSubdependencyFaqs(subdep);
      }

    } catch (error) {
      console.error('❌ Error validando métricas de subdependencias:', error);
    }
  }

  private async validateSubdependencyTramites(subdependencia: any): Promise<void> {
    try {
      const { count, error } = await supabase
        .from('tramites')
        .select('*', { count: 'exact', head: true })
        .eq('subdependencia_id', subdependencia.id);

      if (error) {
        this.results.push({
          entity: `Subdependencia ${subdependencia.nombre} - Trámites`,
          expected: subdependencia.total_tramites,
          actual: 0,
          status: 'FAIL',
          error: error.message
        });
        return;
      }

      const actualCount = count || 0;
      const expectedCount = subdependencia.total_tramites || 0;

      this.results.push({
        entity: `Subdependencia ${subdependencia.nombre} - Trámites`,
        expected: expectedCount,
        actual: actualCount,
        status: actualCount === expectedCount ? 'PASS' : 'FAIL'
      });

    } catch (error) {
      this.results.push({
        entity: `Subdependencia ${subdependencia.nombre} - Trámites`,
        expected: subdependencia.total_tramites,
        actual: 0,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  private async validateSubdependencyOpas(subdependencia: any): Promise<void> {
    try {
      const { count, error } = await supabase
        .from('opas')
        .select('*', { count: 'exact', head: true })
        .eq('subdependencia_id', subdependencia.id);

      if (error) {
        this.results.push({
          entity: `Subdependencia ${subdependencia.nombre} - OPAs`,
          expected: subdependencia.total_opas,
          actual: 0,
          status: 'FAIL',
          error: error.message
        });
        return;
      }

      const actualCount = count || 0;
      const expectedCount = subdependencia.total_opas || 0;

      this.results.push({
        entity: `Subdependencia ${subdependencia.nombre} - OPAs`,
        expected: expectedCount,
        actual: actualCount,
        status: actualCount === expectedCount ? 'PASS' : 'FAIL'
      });

    } catch (error) {
      this.results.push({
        entity: `Subdependencia ${subdependencia.nombre} - OPAs`,
        expected: subdependencia.total_opas,
        actual: 0,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  private async validateSubdependencyFaqs(subdependencia: any): Promise<void> {
    try {
      const { count, error } = await supabase
        .from('faqs')
        .select('*', { count: 'exact', head: true })
        .eq('subdependencia_id', subdependencia.id);

      if (error) {
        this.results.push({
          entity: `Subdependencia ${subdependencia.nombre} - FAQs`,
          expected: subdependencia.total_faqs,
          actual: 0,
          status: 'FAIL',
          error: error.message
        });
        return;
      }

      const actualCount = count || 0;
      const expectedCount = subdependencia.total_faqs || 0;

      this.results.push({
        entity: `Subdependencia ${subdependencia.nombre} - FAQs`,
        expected: expectedCount,
        actual: actualCount,
        status: actualCount === expectedCount ? 'PASS' : 'FAIL'
      });

    } catch (error) {
      this.results.push({
        entity: `Subdependencia ${subdependencia.nombre} - FAQs`,
        expected: subdependencia.total_faqs,
        actual: 0,
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  }

  async validateGlobalCounts(): Promise<void> {
    console.log('🔍 Validando conteos globales...');

    try {
      // Validar conteo total de trámites
      const { count: tramitesCount, error: tramitesError } = await supabase
        .from('tramites')
        .select('*', { count: 'exact', head: true });

      if (!tramitesError) {
        console.log(`📊 Total de trámites: ${tramitesCount}`);
      }

      // Validar conteo total de OPAs
      const { count: opasCount, error: opasError } = await supabase
        .from('opas')
        .select('*', { count: 'exact', head: true });

      if (!opasError) {
        console.log(`📊 Total de OPAs: ${opasCount}`);
      }

      // Validar conteo total de FAQs
      const { count: faqsCount, error: faqsError } = await supabase
        .from('faqs')
        .select('*', { count: 'exact', head: true });

      if (!faqsError) {
        console.log(`📊 Total de FAQs: ${faqsCount}`);
      }

      // Validar conteo de dependencias
      const { count: depsCount, error: depsError } = await supabase
        .from('dependencias')
        .select('*', { count: 'exact', head: true });

      if (!depsError) {
        console.log(`📊 Total de dependencias: ${depsCount}`);
      }

      // Validar conteo de subdependencias
      const { count: subdepsCount, error: subdepsError } = await supabase
        .from('subdependencias')
        .select('*', { count: 'exact', head: true });

      if (!subdepsError) {
        console.log(`📊 Total de subdependencias: ${subdepsCount}`);
      }

    } catch (error) {
      console.error('❌ Error validando conteos globales:', error);
    }
  }

  async generateReport(): Promise<MetricsReport> {
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;

    // Obtener conteos para el resumen
    const { count: tramitesCount } = await supabase
      .from('tramites')
      .select('*', { count: 'exact', head: true });

    const { count: opasCount } = await supabase
      .from('opas')
      .select('*', { count: 'exact', head: true });

    const { count: faqsCount } = await supabase
      .from('faqs')
      .select('*', { count: 'exact', head: true });

    const { count: depsCount } = await supabase
      .from('dependencias')
      .select('*', { count: 'exact', head: true });

    const { count: subdepsCount } = await supabase
      .from('subdependencias')
      .select('*', { count: 'exact', head: true });

    return {
      timestamp: new Date().toISOString(),
      totalValidations: this.results.length,
      passed,
      failed,
      results: this.results,
      summary: {
        dependencias: depsCount || 0,
        subdependencias: subdepsCount || 0,
        tramites: tramitesCount || 0,
        opas: opasCount || 0,
        faqs: faqsCount || 0
      }
    };
  }

  printReport(report: MetricsReport): void {
    console.log('\n📋 REPORTE DE VALIDACIÓN DE MÉTRICAS');
    console.log('=====================================');
    console.log(`🕐 Timestamp: ${report.timestamp}`);
    console.log(`📊 Total validaciones: ${report.totalValidations}`);
    console.log(`✅ Exitosas: ${report.passed}`);
    console.log(`❌ Fallidas: ${report.failed}`);
    console.log(`📈 Tasa de éxito: ${((report.passed / report.totalValidations) * 100).toFixed(1)}%`);

    console.log('\n📊 RESUMEN DEL SISTEMA:');
    console.log(`• Dependencias: ${report.summary.dependencias}`);
    console.log(`• Subdependencias: ${report.summary.subdependencias}`);
    console.log(`• Trámites: ${report.summary.tramites}`);
    console.log(`• OPAs: ${report.summary.opas}`);
    console.log(`• FAQs: ${report.summary.faqs}`);

    if (report.failed > 0) {
      console.log('\n❌ VALIDACIONES FALLIDAS:');
      report.results
        .filter(r => r.status === 'FAIL')
        .forEach(result => {
          console.log(`• ${result.entity}: Esperado ${result.expected}, Actual ${result.actual}`);
          if (result.error) {
            console.log(`  Error: ${result.error}`);
          }
        });
    }

    console.log('\n=====================================');
  }
}

async function main() {
  console.log('🚀 Iniciando validación de métricas del sistema CHIA...\n');

  const validator = new MetricsValidator();

  try {
    // Ejecutar validaciones
    await validator.validateGlobalCounts();
    await validator.validateDependencyMetrics();
    await validator.validateSubdependencyMetrics();

    // Generar y mostrar reporte
    const report = await validator.generateReport();
    validator.printReport(report);

    // Salir con código de error si hay fallas
    if (report.failed > 0) {
      process.exit(1);
    }

    console.log('\n✅ Todas las validaciones pasaron exitosamente!');

  } catch (error) {
    console.error('💥 Error durante la validación:', error);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}
