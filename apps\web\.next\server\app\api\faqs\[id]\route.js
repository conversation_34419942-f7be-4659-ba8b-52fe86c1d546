(()=>{var e={};e.id=3461,e.ids=[3461],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15931:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>c,GET:()=>u,PUT:()=>p});var a=t(48106),i=t(48819),n=t(12050),o=t(4235),d=t(73474);async function u(e,{params:r}){try{let{id:e}=r,t=(0,d.y8)(),{data:s,error:a}=await t.from("faqs_view").select("*").eq("id",e).single();if(a||!s)return o.NextResponse.json({error:"FAQ no encontrada"},{status:404});let i={id:s.id,tema:s.tema,pregunta:s.pregunta,respuesta:s.respuesta,palabrasClave:s.palabras_clave||[],prioridad:s.prioridad||0,vistas:s.vistas||0,utilidad:s.utilidad||0,dependencia:{id:s.dependencia_id,codigo:s.dependencia_codigo,nombre:s.dependencia_nombre,sigla:s.dependencia_sigla},subdependencia:{id:s.subdependencia_id,nombre:s.subdependencia_nombre}};return o.NextResponse.json({success:!0,data:i})}catch(e){return console.error("Unexpected error in FAQ detail API:",e),o.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function p(e,{params:r}){try{let{id:t}=r,s=await e.json(),a=(0,d.y8)(),{data:{user:i},error:n}=await a.auth.getUser();if(n||!i)return o.NextResponse.json({error:"No autorizado"},{status:401});let{data:u,error:p}=await a.schema("ingestion").from("faqs").select("subdependencia_id, subdependencias(dependencia_id)").eq("id",t).single();if(p||!u)return o.NextResponse.json({error:"FAQ no encontrada"},{status:404});let{data:c}=await a.rpc("check_user_permission",{p_user_id:i.id,p_action:"UPDATE",p_table_name:"faqs",p_dependencia_id:u.subdependencias?.dependencia_id});if(!c)return o.NextResponse.json({error:"Sin permisos para modificar esta FAQ"},{status:403});let{data:l,error:x}=await a.schema("ingestion").from("faqs").update({...s,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(x)return console.error("Error updating FAQ:",x),o.NextResponse.json({error:"Error al actualizar FAQ"},{status:500});return o.NextResponse.json({success:!0,data:l})}catch(e){return console.error("Error in FAQ PUT:",e),o.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function c(e,{params:r}){try{let{id:e}=r,t=(0,d.y8)(),{data:{user:s},error:a}=await t.auth.getUser();if(a||!s)return o.NextResponse.json({error:"No autorizado"},{status:401});let{data:i,error:n}=await t.schema("ingestion").from("faqs").select("subdependencia_id, subdependencias(dependencia_id)").eq("id",e).single();if(n||!i)return o.NextResponse.json({error:"FAQ no encontrada"},{status:404});let{data:u}=await t.rpc("check_user_permission",{p_user_id:s.id,p_action:"DELETE",p_table_name:"faqs",p_dependencia_id:i.subdependencias?.dependencia_id});if(!u)return o.NextResponse.json({error:"Sin permisos para eliminar esta FAQ"},{status:403});let{error:p}=await t.schema("ingestion").from("faqs").update({activo:!1,updated_at:new Date().toISOString()}).eq("id",e);if(p)return console.error("Error deactivating FAQ:",p),o.NextResponse.json({error:"Error al eliminar FAQ"},{status:500});return o.NextResponse.json({success:!0,message:"FAQ eliminada correctamente"})}catch(e){return console.error("Error in FAQ DELETE:",e),o.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/faqs/[id]/route",pathname:"/api/faqs/[id]",filename:"route",bundlePath:"app/api/faqs/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\faqs\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:f}=l;function _(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,r,t)=>{"use strict";t.d(r,{y8:()=>n});var s=t(2492);let a="https://hndowofzjzjoljnapokv.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(process.env.SUPABASE_SERVICE_ROLE_KEY,!a||!i)throw Error("Missing Supabase environment variables");function n(){return(0,s.UU)(a,i)}n()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744],()=>t(15931));module.exports=s})();