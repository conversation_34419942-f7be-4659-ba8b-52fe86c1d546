npm verbose cli C:\Program Files\nodejs\node.exe C:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js
npm info using npm@10.9.0
npm info using node@v23.2.0
npm info config found workspace root at C:\Users\<USER>\Documents\augment-projects\chia-next
npm verbose title npm run test
npm verbose argv "run" "test"
npm verbose logfile logs-max:10 dir:C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-07-09T02_28_20_334Z-
npm verbose logfile C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-07-09T02_28_20_334Z-debug-0.log

> @chia/auth@0.1.0 test
> jest

No tests found, exiting with code 1
Run with `--passWithNoTests` to exit with code 0
In C:\Users\<USER>\Documents\augment-projects\chia-next\packages\auth
  23 files checked.
  testMatch: **/__tests__/**/*.[jt]s?(x), **/?(*.)+(spec|test).[tj]s?(x) - 0 matches
  testPathIgnorePatterns: \\node_modules\\ - 23 matches
  testRegex:  - 0 matches
Pattern:  - 0 matches
npm verbose stack Error: command failed
npm verbose stack     at promiseSpawn (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\promise-spawn\lib\index.js:22:22)
npm verbose stack     at spawnWithShell (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\promise-spawn\lib\index.js:124:10)
npm verbose stack     at promiseSpawn (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\promise-spawn\lib\index.js:12:12)
npm verbose stack     at runScriptPkg (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\run-script\lib\run-script-pkg.js:77:13)
npm verbose stack     at runScript (C:\Program Files\nodejs\node_modules\npm\node_modules\@npmcli\run-script\lib\run-script.js:9:12)
npm verbose stack     at #run (C:\Program Files\nodejs\node_modules\npm\lib\commands\run-script.js:131:13)
npm verbose stack     at RunScript.execWorkspaces (C:\Program Files\nodejs\node_modules\npm\lib\commands\run-script.js:63:24)
npm verbose stack     at async Npm.exec (C:\Program Files\nodejs\node_modules\npm\lib\npm.js:207:9)
npm verbose stack     at async module.exports (C:\Program Files\nodejs\node_modules\npm\lib\cli\entry.js:74:5)
npm verbose pkgid @chia/auth@0.1.0
npm error Lifecycle script `test` failed with error:
npm error code 1
npm error path C:\Users\<USER>\Documents\augment-projects\chia-next\packages\auth
npm error workspace @chia/auth@0.1.0
npm error location C:\Users\<USER>\Documents\augment-projects\chia-next\packages\auth
npm error command failed
npm error command C:\WINDOWS\system32\cmd.exe /d /s /c jest
npm verbose cwd C:\Users\<USER>\Documents\augment-projects\chia-next\packages\auth
npm verbose os Windows_NT 10.0.26100
npm verbose node v23.2.0
npm verbose npm  v10.9.0
npm verbose exit 1
npm verbose code 1
