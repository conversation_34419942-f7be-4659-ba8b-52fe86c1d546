import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';

/**
 * GET /api/opas/[id]
 * Obtiene detalle de una OPA específica
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = createServerSupabase();

    const { data: opa, error } = await supabase
      .from('opas_view')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !opa) {
      return NextResponse.json(
        { error: 'OPA no encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: opa
    });

  } catch (error) {
    console.error('Unexpected error in OPA detail API:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/opas/[id]
 * Actualiza una OPA existente
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const updates = await request.json();

    const supabase = createServerSupabase();

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener OPA actual para verificar permisos
    const { data: currentOpa, error: fetchError } = await supabase
      .schema('ingestion')
      .from('opas')
      .select('subdependencia_id, subdependencias(dependencia_id)')
      .eq('id', id)
      .single();

    if (fetchError || !currentOpa) {
      return NextResponse.json(
        { error: 'OPA no encontrada' },
        { status: 404 }
      );
    }

    // Verificar permisos
    const { data: hasPermission } = await supabase
      .rpc('check_user_permission', {
        p_user_id: user.id,
        p_action: 'UPDATE',
        p_table_name: 'opas',
        p_dependencia_id: currentOpa.subdependencias?.dependencia_id
      });

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Sin permisos para modificar esta OPA' },
        { status: 403 }
      );
    }

    // Actualizar OPA
    const { data: updatedOpa, error: updateError } = await supabase
      .schema('ingestion')
      .from('opas')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating OPA:', updateError);
      return NextResponse.json(
        { error: 'Error al actualizar OPA' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedOpa
    });

  } catch (error) {
    console.error('Error in OPA PUT:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/opas/[id]
 * Desactiva una OPA (soft delete)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = createServerSupabase();

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener OPA actual para verificar permisos
    const { data: currentOpa, error: fetchError } = await supabase
      .schema('ingestion')
      .from('opas')
      .select('subdependencia_id, subdependencias(dependencia_id)')
      .eq('id', id)
      .single();

    if (fetchError || !currentOpa) {
      return NextResponse.json(
        { error: 'OPA no encontrada' },
        { status: 404 }
      );
    }

    // Verificar permisos
    const { data: hasPermission } = await supabase
      .rpc('check_user_permission', {
        p_user_id: user.id,
        p_action: 'DELETE',
        p_table_name: 'opas',
        p_dependencia_id: currentOpa.subdependencias?.dependencia_id
      });

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Sin permisos para eliminar esta OPA' },
        { status: 403 }
      );
    }

    // Desactivar OPA (soft delete)
    const { error: deleteError } = await supabase
      .schema('ingestion')
      .from('opas')
      .update({
        activo: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (deleteError) {
      console.error('Error deactivating OPA:', deleteError);
      return NextResponse.json(
        { error: 'Error al eliminar OPA' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'OPA eliminada correctamente'
    });

  } catch (error) {
    console.error('Error in OPA DELETE:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
