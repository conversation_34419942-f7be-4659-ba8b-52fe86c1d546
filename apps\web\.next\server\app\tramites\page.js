(()=>{var e={};e.id=6870,e.ids=[6870],e.modules={1171:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\tramites\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\tramites\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23365:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(84667).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},25008:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var t=a(24332),r=a(48819),n=a(67851),i=a.n(n),l=a(97540),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(s,c);let d={children:["",{children:["tramites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1171)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\tramites\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\tramites\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/tramites/page",pathname:"/tramites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35795:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(84667).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},39662:(e,s,a)=>{"use strict";a.d(s,{pd:()=>t.pd});var t=a(91823)},59327:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(84667).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67370:(e,s,a)=>{Promise.resolve().then(a.bind(a,91133))},74845:(e,s,a)=>{Promise.resolve().then(a.bind(a,1171))},84396:(e,s,a)=>{"use strict";a.d(s,{bq:()=>t.bq,eb:()=>t.eb,gC:()=>t.gC,l6:()=>t.l6,yv:()=>t.yv});var t=a(91823)},91133:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(13486),r=a(60159),n=a(2984),i=a(68713),l=a(95194),c=a(26791),d=a(39662),o=a(81480),m=a(84396),x=a(43967),p=a(72513),h=a(35795),u=a(95827),g=a(99672),j=a(23365),v=a(59327),b=a(49989),y=a.n(b);function N(){let e=(0,n.useSearchParams)(),s=e.get("dependencia"),a=e.get("subdependencia"),[b,N]=(0,r.useState)([]),[f,w]=(0,r.useState)(!0),[P,C]=(0,r.useState)(null),[A,k]=(0,r.useState)(""),[_,q]=(0,r.useState)(""),[E,M]=(0,r.useState)(s||""),[S,$]=(0,r.useState)([]),[D,T]=(0,r.useState)([]),[R,U]=(0,r.useState)(1),[z,J]=(0,r.useState)({page:1,limit:12,total:0,totalPages:0}),G=async()=>{try{w(!0);let e=new URLSearchParams({page:R.toString(),limit:"12"});A&&e.append("search",A),_&&e.append("categoria",_),E&&e.append("dependencia",E),a&&e.append("subdependencia",a);let s=await fetch(`/api/tramites?${e}`);if(!s.ok)throw Error("Error al cargar tr\xe1mites");let t=await s.json();if(t.success)N(t.data),J(t.pagination);else throw Error(t.error||"Error desconocido")}catch(e){C(e instanceof Error?e.message:"Error desconocido")}finally{w(!1)}},L=e=>{k(e),U(1)},B=()=>{k(""),q(""),M(""),U(1)};return f&&0===b.length?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(i.MainNavigation,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Cargando tr\xe1mites..."})]})})})]}):P?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(i.MainNavigation,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-red-500 text-xl mb-4",children:"⚠️"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error al cargar"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:P}),(0,t.jsx)(o.$n,{onClick:G,children:"Reintentar"})]})})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(i.MainNavigation,{}),(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(s||a)&&(0,t.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-6",children:[(0,t.jsx)(y(),{href:"/dependencias",className:"hover:text-blue-600",children:"Dependencias"}),s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)(y(),{href:`/dependencias/${s}`,className:"hover:text-blue-600",children:D.find(e=>e.id===s)?.nombre||"Dependencia"})]}),(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-gray-900",children:"Tr\xe1mites"})]}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(p.A,{className:"h-6 w-6 text-green-600"})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Tr\xe1mites Municipales"})]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Encuentra y consulta todos los tr\xe1mites disponibles en el municipio de Ch\xeda."})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-6 mb-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(d.pd,{type:"text",placeholder:"Buscar tr\xe1mites...",value:A,onChange:e=>L(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(m.l6,{value:_||"all",onValueChange:e=>{q("all"===e?"":e),U(1)},children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"Todas las categor\xedas"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"Todas las categor\xedas"}),S.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))]})]}),(0,t.jsxs)(m.l6,{value:E||"all",onValueChange:e=>{M("all"===e?"":e),U(1)},children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"Todas las dependencias"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"Todas las dependencias"}),D.map(e=>(0,t.jsx)(m.eb,{value:e.id,children:e.nombre},e.id))]})]}),(0,t.jsxs)(o.$n,{variant:"outline",onClick:B,children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Limpiar Filtros"]})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("p",{className:"text-gray-600",children:["Mostrando ",b.length," de ",z.total," tr\xe1mites"]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["P\xe1gina ",z.page," de ",z.totalPages]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:b.map(e=>(0,t.jsxs)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsx)(l.ZB,{className:"text-lg line-clamp-2",children:(0,t.jsx)(y(),{href:`/tramites/${e.id}`,className:"hover:text-green-600 transition-colors",children:e.nombre})}),(0,t.jsx)(c.E,{variant:"secondary",className:"ml-2 shrink-0",children:e.categoria})]}),e.descripcion&&(0,t.jsx)(l.BT,{className:"line-clamp-3",children:e.descripcion})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,t.jsxs)("span",{className:"truncate",children:[e.dependencia_nombre,e.subdependencia_nombre&&` - ${e.subdependencia_nombre}`]})]}),e.tiempo_estimado&&(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{children:e.tiempo_estimado})]}),e.costo&&(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{children:e.costo})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t",children:[(0,t.jsx)(c.E,{variant:"activo"===e.estado?"default":"secondary",className:"text-xs",children:e.estado}),(0,t.jsx)(y(),{href:`/tramites/${e.id}`,children:(0,t.jsx)(o.$n,{variant:"outline",size:"sm",children:"Ver Detalle"})})]})]})})]},e.id))}),z.totalPages>1&&(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)(o.$n,{variant:"outline",onClick:()=>U(Math.max(1,R-1)),disabled:1===R||f,children:"Anterior"}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,z.totalPages)},(e,s)=>{let a=s+1;return(0,t.jsx)(o.$n,{variant:R===a?"default":"outline",size:"sm",onClick:()=>U(a),disabled:f,children:a},a)})}),(0,t.jsx)(o.$n,{variant:"outline",onClick:()=>U(Math.min(z.totalPages,R+1)),disabled:R===z.totalPages||f,children:"Siguiente"})]}),0===b.length&&!f&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCC4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No se encontraron tr\xe1mites"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Intenta ajustar los filtros de b\xfasqueda"}),(0,t.jsx)(o.$n,{onClick:B,children:"Limpiar Filtros"})]})]})]})}},95827:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(84667).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[191,7118,114,684,8686,5194,3536],()=>a(25008));module.exports=t})();