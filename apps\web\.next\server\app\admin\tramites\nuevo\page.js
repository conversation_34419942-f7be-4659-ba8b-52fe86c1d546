(()=>{var e={};e.id=3434,e.ids=[3434],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15998:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(84667).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39662:(e,s,r)=>{"use strict";r.d(s,{pd:()=>a.pd});var a=r(91823)},43533:(e,s,r)=>{Promise.resolve().then(r.bind(r,67436))},49903:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\admin\\\\tramites\\\\nuevo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\tramites\\nuevo\\page.tsx","default")},55965:(e,s,r)=>{"use strict";r.d(s,{J:()=>c});var a=r(13486),i=r(60159),t=r(94108),n=i.forwardRef((e,s)=>(0,a.jsx)(t.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var d=r(76353),l=r(67499);let o=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...s},r)=>(0,a.jsx)(n,{ref:r,className:(0,l.cn)(o(),e),...s}));c.displayName=n.displayName},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67436:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var a=r(13486),i=r(60159),t=r(2984),n=r(95194),d=r(39662),l=r(81480),o=r(67499);let c=i.forwardRef(({className:e,...s},r)=>(0,a.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));c.displayName="Textarea";var u=r(84396),m=r(55965),p=r(15998),h=r(74439),x=r(84667);let v=(0,x.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),j=(0,x.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var g=r(49989),b=r.n(g);function f(){let e=(0,t.useRouter)(),[s,r]=(0,i.useState)(!1),[o,x]=(0,i.useState)([]),[g,f]=(0,i.useState)([]),[y,C]=(0,i.useState)(""),[N,w]=(0,i.useState)(""),[q,_]=(0,i.useState)({nombre:"",descripcion:"",categoria:"",dependencia_id:"",subdependencia_id:"",tiempo_estimado:"",costo:"",requisitos:[],documentos_requeridos:[],modalidad:"presencial",estado:"activo",observaciones:"",normativa:""}),P=(e,s)=>{_(r=>({...r,[e]:s}))},k=()=>{y.trim()&&(_(e=>({...e,requisitos:[...e.requisitos,y.trim()]})),C(""))},D=e=>{_(s=>({...s,requisitos:s.requisitos.filter((s,r)=>r!==e)}))},J=()=>{N.trim()&&(_(e=>({...e,documentos_requeridos:[...e.documentos_requeridos,N.trim()]})),w(""))},A=e=>{_(s=>({...s,documentos_requeridos:s.documentos_requeridos.filter((s,r)=>r!==e)}))},E=async s=>{if(s.preventDefault(),!q.nombre||!q.dependencia_id)return void alert("Por favor completa los campos obligatorios");try{r(!0);let s=await fetch("/api/tramites",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(q)});if(s.ok){let r=await s.json();r.success?e.push("/admin/tramites"):alert(r.error||"Error al crear el tr\xe1mite")}else alert("Error al crear el tr\xe1mite")}catch(e){console.error("Error creating tramite:",e),alert("Error al crear el tr\xe1mite")}finally{r(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(b(),{href:"/admin/tramites",children:(0,a.jsxs)(l.$n,{variant:"outline",size:"sm",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Volver"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Nuevo Tr\xe1mite"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Crear un nuevo tr\xe1mite municipal"})]})]}),(0,a.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Informaci\xf3n B\xe1sica"}),(0,a.jsx)(n.BT,{children:"Datos principales del tr\xe1mite"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"nombre",children:"Nombre del Tr\xe1mite *"}),(0,a.jsx)(d.pd,{id:"nombre",value:q.nombre,onChange:e=>P("nombre",e.target.value),placeholder:"Ej: Certificado de Residencia",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"categoria",children:"Categor\xeda"}),(0,a.jsx)(d.pd,{id:"categoria",value:q.categoria,onChange:e=>P("categoria",e.target.value),placeholder:"Ej: Certificados"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"descripcion",children:"Descripci\xf3n"}),(0,a.jsx)(c,{id:"descripcion",value:q.descripcion,onChange:e=>P("descripcion",e.target.value),placeholder:"Describe brevemente el tr\xe1mite...",rows:3})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Organizaci\xf3n"}),(0,a.jsx)(n.BT,{children:"Dependencia responsable del tr\xe1mite"})]}),(0,a.jsx)(n.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"dependencia",children:"Dependencia *"}),(0,a.jsxs)(u.l6,{value:q.dependencia_id,onValueChange:e=>P("dependencia_id",e),children:[(0,a.jsx)(u.bq,{children:(0,a.jsx)(u.yv,{placeholder:"Selecciona una dependencia"})}),(0,a.jsx)(u.gC,{children:o.map(e=>(0,a.jsx)(u.eb,{value:e.id,children:e.nombre},e.id))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"subdependencia",children:"Subdependencia"}),(0,a.jsxs)(u.l6,{value:q.subdependencia_id,onValueChange:e=>P("subdependencia_id",e),disabled:!q.dependencia_id||0===g.length,children:[(0,a.jsx)(u.bq,{children:(0,a.jsx)(u.yv,{placeholder:"Selecciona una subdependencia"})}),(0,a.jsx)(u.gC,{children:g.map(e=>(0,a.jsx)(u.eb,{value:e.id,children:e.nombre},e.id))})]})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Detalles del Tr\xe1mite"}),(0,a.jsx)(n.BT,{children:"Informaci\xf3n espec\xedfica sobre tiempo, costo y modalidad"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"tiempo_estimado",children:"Tiempo Estimado"}),(0,a.jsx)(d.pd,{id:"tiempo_estimado",value:q.tiempo_estimado,onChange:e=>P("tiempo_estimado",e.target.value),placeholder:"Ej: 3-5 d\xedas h\xe1biles"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"costo",children:"Costo"}),(0,a.jsx)(d.pd,{id:"costo",value:q.costo,onChange:e=>P("costo",e.target.value),placeholder:"Ej: $15,000 COP"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"modalidad",children:"Modalidad"}),(0,a.jsxs)(u.l6,{value:q.modalidad,onValueChange:e=>P("modalidad",e),children:[(0,a.jsx)(u.bq,{children:(0,a.jsx)(u.yv,{})}),(0,a.jsxs)(u.gC,{children:[(0,a.jsx)(u.eb,{value:"presencial",children:"Presencial"}),(0,a.jsx)(u.eb,{value:"virtual",children:"Virtual"}),(0,a.jsx)(u.eb,{value:"mixta",children:"Mixta"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"estado",children:"Estado"}),(0,a.jsxs)(u.l6,{value:q.estado,onValueChange:e=>P("estado",e),children:[(0,a.jsx)(u.bq,{className:"w-full md:w-48",children:(0,a.jsx)(u.yv,{})}),(0,a.jsxs)(u.gC,{children:[(0,a.jsx)(u.eb,{value:"activo",children:"Activo"}),(0,a.jsx)(u.eb,{value:"inactivo",children:"Inactivo"}),(0,a.jsx)(u.eb,{value:"borrador",children:"Borrador"})]})]})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Requisitos"}),(0,a.jsx)(n.BT,{children:"Lista de requisitos necesarios para el tr\xe1mite"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(d.pd,{value:y,onChange:e=>C(e.target.value),placeholder:"Agregar nuevo requisito...",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),k())}),(0,a.jsx)(l.$n,{type:"button",onClick:k,children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]}),q.requisitos.length>0&&(0,a.jsx)("div",{className:"space-y-2",children:q.requisitos.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm",children:e}),(0,a.jsx)(l.$n,{type:"button",variant:"ghost",size:"sm",onClick:()=>D(s),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(v,{className:"h-4 w-4"})})]},s))})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Documentos Requeridos"}),(0,a.jsx)(n.BT,{children:"Documentos que debe presentar el ciudadano"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(d.pd,{value:N,onChange:e=>w(e.target.value),placeholder:"Agregar nuevo documento...",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),J())}),(0,a.jsx)(l.$n,{type:"button",onClick:J,children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]}),q.documentos_requeridos.length>0&&(0,a.jsx)("div",{className:"space-y-2",children:q.documentos_requeridos.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("span",{className:"text-sm",children:e}),(0,a.jsx)(l.$n,{type:"button",variant:"ghost",size:"sm",onClick:()=>A(s),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(v,{className:"h-4 w-4"})})]},s))})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Informaci\xf3n Adicional"})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"observaciones",children:"Observaciones"}),(0,a.jsx)(c,{id:"observaciones",value:q.observaciones,onChange:e=>P("observaciones",e.target.value),placeholder:"Observaciones adicionales...",rows:3})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{htmlFor:"normativa",children:"Marco Normativo"}),(0,a.jsx)(c,{id:"normativa",value:q.normativa,onChange:e=>P("normativa",e.target.value),placeholder:"Leyes, decretos o resoluciones que aplican...",rows:3})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-4",children:[(0,a.jsx)(b(),{href:"/admin/tramites",children:(0,a.jsx)(l.$n,{variant:"outline",type:"button",children:"Cancelar"})}),(0,a.jsx)(l.$n,{type:"submit",disabled:s,children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Guardando..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j,{className:"h-4 w-4 mr-2"}),"Crear Tr\xe1mite"]})})]})]})]})}},74439:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(84667).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},77768:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=r(24332),i=r(48819),t=r(67851),n=r.n(t),d=r(97540),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(s,l);let o={children:["",{children:["admin",{children:["tramites",{children:["nuevo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49903)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\tramites\\nuevo\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,47672)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\tramites\\nuevo\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/tramites/nuevo/page",pathname:"/admin/tramites/nuevo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},84396:(e,s,r)=>{"use strict";r.d(s,{bq:()=>a.bq,eb:()=>a.eb,gC:()=>a.gC,l6:()=>a.l6,yv:()=>a.yv});var a=r(91823)},94108:(e,s,r)=>{"use strict";r.d(s,{sG:()=>n});var a=r(60159);r(22358);var i=r(90691),t=r(13486),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let r=(0,i.TL)(`Primitive.${s}`),n=a.forwardRef((e,a)=>{let{asChild:i,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,t.jsx)(i?r:s,{...n,ref:a})});return n.displayName=`Primitive.${s}`,{...e,[s]:n}},{})},96269:(e,s,r)=>{Promise.resolve().then(r.bind(r,49903))}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[191,7118,114,684,5194,7269],()=>r(77768));module.exports=a})();