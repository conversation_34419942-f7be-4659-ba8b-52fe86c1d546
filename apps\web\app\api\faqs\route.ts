import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const tema = searchParams.get('tema');
    const dependencia = searchParams.get('dependencia');
    const search = searchParams.get('search');

    let query = supabase
      .from('faqs_view')
      .select('*')
      .order('tema')
      .range(offset, offset + limit - 1);

    // Apply filters
    if (tema) {
      query = query.ilike('tema', `%${tema}%`);
    }
    
    if (dependencia) {
      query = query.eq('dependencia_id', dependencia);
    }

    // Apply search filter using ilike for now
    if (search) {
      query = query.or(`pregunta.ilike.%${search}%,respuesta.ilike.%${search}%`);
    }

    const { data: faqs, error, count } = await query;

    if (error) {
      console.error('Error fetching FAQs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch FAQs', details: error.message },
        { status: 500 }
      );
    }

    // Transform data to match frontend expectations
    const transformedFaqs = faqs?.map(faq => ({
      id: faq.id,
      tema: faq.tema,
      pregunta: faq.pregunta,
      respuesta: faq.respuesta,
      palabrasClave: [],
      prioridad: 0,
      vistas: 0,
      utilidad: 0,
      dependencia: {
        id: faq.dependencia_id,
        codigo: faq.dependencia_codigo,
        nombre: faq.dependencia_nombre,
        sigla: faq.dependencia_sigla
      }
    })) || [];

    return NextResponse.json({
      success: true,
      data: transformedFaqs,
      pagination: {
        limit,
        offset,
        total: count || transformedFaqs.length
      },
      filters: {
        tema,
        dependencia,
        search
      }
    });

  } catch (error) {
    console.error('Unexpected error in FAQs API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// Get topics for filtering
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'get_topics') {
      const supabase = createServerSupabase();

      const { data: topics, error } = await supabase
        .from('faqs_view')
        .select('tema')
        .not('tema', 'is', null);

      if (error) {
        throw error;
      }

      const uniqueTopics = [...new Set(topics?.map(f => f.tema))].filter(Boolean);

      return NextResponse.json({
        success: true,
        data: uniqueTopics
      });
    }

    if (action === 'increment_views') {
      const { faqId } = await request.json();
      const supabase = createServerSupabase();
      
      // First get current views count
      const { data: currentFaq } = await supabase
        .schema('ingestion')
        .from('faqs')
        .select('vistas')
        .eq('id', faqId)
        .single();

      const newViews = (currentFaq?.vistas || 0) + 1;

      const { error } = await supabase
        .schema('ingestion')
        .from('faqs')
        .update({ vistas: newViews })
        .eq('id', faqId);

      if (error) {
        throw error;
      }

      return NextResponse.json({
        success: true,
        message: 'Views incremented'
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in FAQs POST:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
