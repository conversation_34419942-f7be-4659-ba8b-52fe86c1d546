import { createServerSupabase } from './supabase-server';
import { redirect } from 'next/navigation';

/**
 * Get the current user session on the server side
 * Redirects to login if no session exists
 */
export async function getServerSession() {
  const supabase = createServerSupabase();

  const {
    data: { session },
    error,
  } = await supabase.auth.getSession();

  if (error) {
    console.error('Error getting session:', error);
    redirect('/auth/login');
  }

  if (!session) {
    redirect('/auth/login');
  }

  return session;
}

/**
 * Get the current user session on the server side
 * Returns null if no session exists (doesn't redirect)
 */
export async function getOptionalServerSession() {
  const supabase = createServerSupabase();

  const {
    data: { session },
    error,
  } = await supabase.auth.getSession();

  if (error) {
    console.error('Error getting session:', error);
    return null;
  }

  return session;
}

/**
 * Get the current user profile from the database
 */
export async function getCurrentUserProfile() {
  const session = await getServerSession();
  const supabase = createServerSupabase();

  const { data: profile, error } = await supabase
    .from('ciudadanos')
    .select('*')
    .eq('auth_id', session.user.id)
    .single();

  if (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }

  return profile;
}

/**
 * Sign out the current user
 */
export async function signOut() {
  const supabase = createServerSupabase();

  const { error } = await supabase.auth.signOut();
  
  if (error) {
    console.error('Error signing out:', error);
    throw error;
  }
  
  redirect('/auth/login');
}
