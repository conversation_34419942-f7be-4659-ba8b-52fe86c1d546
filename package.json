{"name": "chia-next", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "setup": "./scripts/setup.sh", "ingestion": "tsx scripts/run-ingestion.ts", "ingestion:all": "tsx scripts/run-ingestion.ts all", "ingestion:faqs": "tsx scripts/run-ingestion.ts faqs", "ingestion:tramites": "tsx scripts/run-ingestion.ts tramites", "ingestion:opas": "tsx scripts/run-ingestion.ts opas", "ingestion:stats": "tsx scripts/run-ingestion.ts stats", "ingestion:search": "tsx scripts/run-ingestion.ts search", "ingestion:cleanup": "tsx scripts/run-ingestion.ts cleanup", "ingestion:dry-run": "tsx scripts/run-ingestion.ts all --dry-run", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "validate-metrics": "tsx scripts/validate-metrics.ts", "performance-check": "tsx scripts/performance-optimization.ts"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "tsx": "^4.7.0", "turbo": "^1.10.0", "typescript": "^5.3.0"}, "eslintConfig": {"extends": ["next/core-web-vitals", "@typescript-eslint/recommended"]}, "dependencies": {"@kayvan/markdown-tree-parser": "^1.6.0", "@supabase/supabase-js": "^2.39.0", "@tailwindcss/line-clamp": "^0.4.4", "@types/lodash": "^4.17.20", "dotenv": "^16.3.0", "framer-motion": "^12.23.0", "lodash": "^4.17.21", "uuid": "^9.0.0"}}