(()=>{var e={};e.id=2337,e.ids=[2337],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47812:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>m,serverHooks:()=>_,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>p,GET:()=>u,PUT:()=>c});var i=t(48106),o=t(48819),n=t(12050),a=t(4235),d=t(73474);async function u(e,{params:r}){try{let{id:e}=r,t=(0,d.y8)(),{data:s,error:i}=await t.from("tramites_view").select("*").eq("id",e).single();if(i||!s)return a.NextResponse.json({error:"Tr\xe1mite no encontrado"},{status:404});let o={id:s.id,nombre:s.nombre,descripcion:s.descripcion||"",categoria:s.categoria||"General",tiempoRespuesta:s.tiempo_estimado||"No especificado",tienePago:s.costo?"S\xed":"No",costoDetalle:s.costo,modalidad:s.modalidad||"Presencial",requisitos:s.requisitos||[],documentosRequeridos:s.documentos_requeridos||[],urlSuit:s.url_suit||"",urlGovco:s.url_govco||"",popularidad:0,satisfaccion:0,dependencia:{id:s.dependencia_id,codigo:s.dependencia_codigo,nombre:s.dependencia_nombre,sigla:s.dependencia_sigla},subdependencia:{id:s.subdependencia_id,nombre:s.subdependencia_nombre}};return a.NextResponse.json({success:!0,data:o})}catch(e){return console.error("Unexpected error in tramite detail API:",e),a.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function c(e,{params:r}){try{let{id:t}=r,s=await e.json(),i=(0,d.y8)(),{data:{user:o},error:n}=await i.auth.getUser();if(n||!o)return a.NextResponse.json({error:"No autorizado"},{status:401});let{data:u,error:c}=await i.schema("ingestion").from("tramites").select("subdependencia_id, subdependencias(dependencia_id)").eq("id",t).single();if(c||!u)return a.NextResponse.json({error:"Tr\xe1mite no encontrado"},{status:404});let{data:p}=await i.rpc("check_user_permission",{p_user_id:o.id,p_action:"UPDATE",p_table_name:"tramites",p_dependencia_id:u.subdependencias?.dependencia_id});if(!p)return a.NextResponse.json({error:"Sin permisos para modificar este tr\xe1mite"},{status:403});let{data:m,error:l}=await i.schema("ingestion").from("tramites").update({...s,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(l)return console.error("Error updating tramite:",l),a.NextResponse.json({error:"Error al actualizar tr\xe1mite"},{status:500});return a.NextResponse.json({success:!0,data:m})}catch(e){return console.error("Error in tramite PUT:",e),a.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function p(e,{params:r}){try{let{id:e}=r,t=(0,d.y8)(),{data:{user:s},error:i}=await t.auth.getUser();if(i||!s)return a.NextResponse.json({error:"No autorizado"},{status:401});let{data:o,error:n}=await t.schema("ingestion").from("tramites").select("subdependencia_id, subdependencias(dependencia_id)").eq("id",e).single();if(n||!o)return a.NextResponse.json({error:"Tr\xe1mite no encontrado"},{status:404});let{data:u}=await t.rpc("check_user_permission",{p_user_id:s.id,p_action:"DELETE",p_table_name:"tramites",p_dependencia_id:o.subdependencias?.dependencia_id});if(!u)return a.NextResponse.json({error:"Sin permisos para eliminar este tr\xe1mite"},{status:403});let{error:c}=await t.schema("ingestion").from("tramites").update({activo:!1,updated_at:new Date().toISOString()}).eq("id",e);if(c)return console.error("Error deactivating tramite:",c),a.NextResponse.json({error:"Error al eliminar tr\xe1mite"},{status:500});return a.NextResponse.json({success:!0,message:"Tr\xe1mite eliminado correctamente"})}catch(e){return console.error("Error in tramite DELETE:",e),a.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/tramites/[id]/route",pathname:"/api/tramites/[id]",filename:"route",bundlePath:"app/api/tramites/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\tramites\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:_}=m;function g(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,r,t)=>{"use strict";t.d(r,{y8:()=>n});var s=t(2492);let i="https://hndowofzjzjoljnapokv.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(process.env.SUPABASE_SERVICE_ROLE_KEY,!i||!o)throw Error("Missing Supabase environment variables");function n(){return(0,s.UU)(i,o)}n()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744],()=>t(47812));module.exports=s})();