(()=>{var e={};e.id=2772,e.ids=[2772],e.modules={135:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(48106),n=t(48819),o=t(12050),a=t(4235),u=t(73474);async function p(){try{let e=(0,u.y8)(),{data:r,error:t}=await e.schema("ingestion").from("dependencias").select("count").limit(1);if(t)throw Error(`Database connection failed: ${t.message}`);let s=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_ROLE_KEY"].filter(e=>!process.env[e]);if(s.length>0)throw Error(`Missing environment variables: ${s.join(", ")}`);return a.NextResponse.json({status:"healthy",timestamp:new Date().toISOString(),version:process.env.npm_package_version||"1.0.0",environment:"production",database:"connected",services:{supabase:"operational",auth:"operational"}})}catch(e){return console.error("Health check failed:",e),a.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),error:e instanceof Error?e.message:"Unknown error",environment:"production"},{status:503})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:h}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,r,t)=>{"use strict";t.d(r,{y8:()=>o});var s=t(2492);let i="https://hndowofzjzjoljnapokv.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(process.env.SUPABASE_SERVICE_ROLE_KEY,!i||!n)throw Error("Missing Supabase environment variables");function o(){return(0,s.UU)(i,n)}o()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744],()=>t(135));module.exports=s})();