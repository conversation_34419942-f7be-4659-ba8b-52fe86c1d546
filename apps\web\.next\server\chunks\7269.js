exports.id=7269,exports.ids=[7269],exports.modules={272:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"}))})},766:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))})},4162:(e,t,r)=>{Promise.resolve().then(r.bind(r,48688))},5723:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},21971:()=>{},24677:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))})},31452:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},32002:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69355,23)),Promise.resolve().then(r.t.bind(r,54439,23)),Promise.resolve().then(r.t.bind(r,67851,23)),Promise.resolve().then(r.t.bind(r,94730,23)),Promise.resolve().then(r.t.bind(r,19774,23)),Promise.resolve().then(r.t.bind(r,53170,23)),Promise.resolve().then(r.t.bind(r,20968,23)),Promise.resolve().then(r.t.bind(r,78298,23))},32399:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},34356:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>l,viewport:()=>s});var a=r(38828),i=r(7666),n=r.n(i);r(21971);let s={width:"device-width",initialScale:1},l={metadataBase:new URL("https://portal.chia-cundinamarca.gov.co"),title:{default:"CHIA - Portal Ciudadano Digital",template:"%s | CHIA - Portal Ciudadano"},description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Realiza tr\xe1mites en l\xednea, consulta informaci\xf3n municipal y accede a servicios digitales las 24 horas.",keywords:["Ch\xeda","Cundinamarca","servicios ciudadanos","gobierno digital","tr\xe1mites en l\xednea","certificados","impuestos","licencias","portal ciudadano","alcald\xeda","municipio"],authors:[{name:"Alcald\xeda Municipal de Ch\xeda"}],creator:"Alcald\xeda Municipal de Ch\xeda",publisher:"Alcald\xeda Municipal de Ch\xeda",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"es_CO",url:"https://portal.chia-cundinamarca.gov.co",siteName:"CHIA - Portal Ciudadano",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Tr\xe1mites en l\xednea las 24 horas.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Portal Ciudadano de Ch\xeda"}]},twitter:{card:"summary_large_image",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca.",images:["/og-image.jpg"],creator:"@AlcaldiaChia"},alternates:{canonical:"https://portal.chia-cundinamarca.gov.co"},category:"government"};function o({children:e}){return(0,a.jsx)("html",{lang:"es",className:"h-full",children:(0,a.jsx)("body",{className:`${n().className} h-full`,children:(0,a.jsx)("div",{id:"root",className:"min-h-full",children:e})})})}},47672:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\layout.tsx","default")},48688:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(13486),i=r(60159),n=r(2984),s=r(49989),l=r.n(s),o=r(58769),d=r(272),c=r(24677),m=r(766),h=r(54575),u=r(31452),x=r(5723);let p=i.forwardRef(function({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}),v=i.forwardRef(function({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))});var f=r(32399);let g=i.forwardRef(function({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))});var b=r(57469);let w=[{name:"Dashboard",href:"/admin",icon:o.A,description:"Resumen general del sistema"},{name:"Dependencias",href:"/admin/dependencias",icon:d.A,description:"Gesti\xf3n de dependencias y subdependencias"},{name:"Tr\xe1mites",href:"/admin/tramites",icon:c.A,description:"Administrar tr\xe1mites municipales"},{name:"OPAs",href:"/admin/opas",icon:m.A,description:"Gesti\xf3n de procedimientos administrativos"},{name:"FAQs",href:"/admin/faqs",icon:h.A,description:"Preguntas frecuentes"},{name:"Usuarios",href:"/admin/usuarios",icon:u.A,description:"Gesti\xf3n de usuarios y permisos"},{name:"Reportes",href:"/admin/reportes",icon:x.A,description:"Estad\xedsticas y reportes"},{name:"Configuraci\xf3n",href:"/admin/configuracion",icon:p,description:"Configuraci\xf3n del sistema"}];function j({children:e}){let[t,r]=(0,i.useState)(!1),[s,o]=(0,i.useState)(null),[d,c]=(0,i.useState)(!0),m=(0,n.useRouter)(),h=(0,n.usePathname)(),u=e=>"/admin"===e?"/admin"===h:h.startsWith(e);return d?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Verificando permisos..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[t&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 lg:hidden",onClick:()=>r(!1),children:(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75"})}),(0,a.jsx)("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${t?"translate-x-0":"-translate-x-full"}`,children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-4 border-b border-gray-200",children:[(0,a.jsxs)(l(),{href:"/admin",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(v,{className:"h-5 w-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:"CHIA"}),(0,a.jsx)("span",{className:"text-xs text-gray-500 block leading-none",children:"Administraci\xf3n"})]})]}),(0,a.jsx)("button",{onClick:()=>r(!1),className:"lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",children:(0,a.jsx)(f.A,{className:"h-6 w-6"})})]}),(0,a.jsx)("nav",{className:"flex-1 px-4 py-4 space-y-1 overflow-y-auto",children:w.map(e=>(0,a.jsxs)(l(),{href:e.href,className:`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${u(e.href)?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:text-blue-700 hover:bg-gray-50"}`,onClick:()=>r(!1),children:[(0,a.jsx)(e.icon,{className:`mr-3 h-5 w-5 ${u(e.href)?"text-blue-700":"text-gray-400 group-hover:text-blue-700"}`}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{children:e.name}),e.description&&(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]},e.name))}),(0,a.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:s?.name?.charAt(0)||"A"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:s?.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 truncate",children:[s?.role," - ",s?.dependencia]})]})]}),(0,a.jsxs)("button",{onClick:()=>{m.push("/")},className:"w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:[(0,a.jsx)(g,{className:"mr-3 h-4 w-4"}),"Cerrar Sesi\xf3n"]})]})]})}),(0,a.jsxs)("div",{className:"lg:pl-64",children:[(0,a.jsx)("div",{className:"sticky top-0 z-30 bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("button",{onClick:()=>r(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",children:(0,a.jsx)(b.A,{className:"h-6 w-6"})}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsx)(l(),{href:"/",className:"text-sm text-gray-600 hover:text-blue-600 transition-colors",children:"Ver Sitio P\xfablico"})})]})}),(0,a.jsx)("main",{className:"flex-1",children:(0,a.jsx)("div",{className:"py-6",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})]})}},50154:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30385,23)),Promise.resolve().then(r.t.bind(r,33737,23)),Promise.resolve().then(r.t.bind(r,86081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,35856,23)),Promise.resolve().then(r.t.bind(r,55492,23)),Promise.resolve().then(r.t.bind(r,89082,23)),Promise.resolve().then(r.t.bind(r,45812,23))},54575:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z"}))})},55362:()=>{},57469:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))})},58769:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60159);let i=a.forwardRef(function({title:e,titleId:t,...r},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))})},63994:(e,t,r)=>{Promise.resolve().then(r.bind(r,47672))},67499:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(4627),i=r(55855);function n(...e){return(0,i.QP)((0,a.$)(e))}},73514:()=>{},81480:(e,t,r)=>{"use strict";r.d(t,{$n:()=>a.$n});var a=r(91823)}};