'use client';

import { useState } from 'react';
import Link from 'next/link';
import ServicesGrid from './ServicesGrid';
import ServicesSearch from './ServicesSearch';

export default function ServicesPageClient() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('Todos');

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  return (
    <>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">
              Servicios Ciudadanos
            </h1>
            <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
              Accede a todos los servicios digitales del municipio de Chía de forma rápida, 
              segura y desde cualquier lugar.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <ServicesSearch
                onSearch={handleSearch}
                placeholder="Buscar servicios municipales..."
              />
            </div>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <ServicesGrid
          searchQuery={searchQuery}
          selectedCategory={selectedCategory}
        />

        {/* Help Section */}
        <div className="mt-16 bg-white rounded-xl shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            ¿Necesitas ayuda?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Nuestro asistente de IA está disponible 24/7 para ayudarte con cualquier consulta 
            sobre nuestros servicios.
          </p>
          <Link
            href="/chat"
            className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            Hablar con el Asistente IA
          </Link>
        </div>
      </div>
    </>
  );
}
