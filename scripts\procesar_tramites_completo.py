#!/usr/bin/env python3
"""
Script para procesar TODOS los 108 trámites del archivo tramites_chia_optimo.json
Conecta directamente a Supabase y procesa cada trámite individualmente
"""

import json
import os
import sys
from supabase import create_client, Client

def load_tramites_data():
    """Cargar datos del archivo JSON de trámites"""
    try:
        with open('data/tramites_chia_optimo.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Archivo cargado exitosamente: {len(data)} trámites encontrados")
        return data
    except Exception as e:
        print(f"❌ Error cargando archivo: {e}")
        return None

def setup_supabase():
    """Configurar cliente de Supabase"""
    try:
        # Configuración de Supabase (usar variables de entorno en producción)
        url = "https://hndowofzjzjoljnapokv.supabase.co"
        key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU5NzI4NzEsImV4cCI6MjA1MTU0ODg3MX0.Qs8Ky7Wd8Qm9Lp2Nv5Rx6Sz3Tj4Uh7Vi8Wk0Xl1Ym2"

        supabase: Client = create_client(url, key)
        print("✅ Cliente Supabase configurado")
        return supabase
    except Exception as e:
        print(f"❌ Error configurando Supabase: {e}")
        return None

def procesar_tramite_individual(supabase, tramite_data):
    """Procesar un trámite individual usando la función SQL existente"""
    try:
        # Extraer datos del trámite
        nombre = tramite_data.get('Nombre', '')
        formulario = tramite_data.get('Formulario', '')
        tiempo_respuesta = tramite_data.get('Tiempo de respuesta', '')
        tiene_pago = tramite_data.get('¿Tiene pago?', '')
        url_suit = tramite_data.get('Visualización trámite en el SUIT', '')
        url_gov_co = tramite_data.get('Visualización trámite en GOV.CO', '')
        codigo_dependencia = tramite_data.get('codigo_dependencia', '')
        codigo_subdependencia = tramite_data.get('codigo_subdependencia', '')

        # Llamar función SQL para procesar trámite
        result = supabase.rpc('procesar_tramite', {
            'p_nombre': nombre,
            'p_formulario': formulario,
            'p_tiempo_respuesta': tiempo_respuesta,
            'p_tiene_pago': tiene_pago,
            'p_url_suit': url_suit,
            'p_url_gov_co': url_gov_co,
            'p_codigo_dependencia': codigo_dependencia,
            'p_codigo_subdependencia': codigo_subdependencia
        })

        return True, result.data
    except Exception as e:
        return False, str(e)

def main():
    """Función principal"""
    print("🚀 Iniciando procesamiento completo de trámites...")

    # Cargar datos
    tramites_data = load_tramites_data()
    if not tramites_data:
        return

    # Configurar Supabase
    supabase = setup_supabase()
    if not supabase:
        return

    # Procesar cada trámite
    procesados = 0
    errores = 0

    for i, tramite in enumerate(tramites_data, 1):
        print(f"Procesando trámite {i}/{len(tramites_data)}: {tramite.get('Nombre', 'Sin nombre')[:50]}...")

        success, result = procesar_tramite_individual(supabase, tramite)

        if success:
            procesados += 1
            print(f"  ✅ Procesado exitosamente")
        else:
            errores += 1
            print(f"  ❌ Error: {result}")

    # Reporte final
    print(f"\n📊 REPORTE FINAL:")
    print(f"   Total trámites en archivo: {len(tramites_data)}")
    print(f"   Procesados exitosamente: {procesados}")
    print(f"   Errores: {errores}")
    print(f"   Tasa de éxito: {(procesados/len(tramites_data)*100):.1f}%")

if __name__ == "__main__":
    main()