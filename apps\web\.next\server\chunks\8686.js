exports.id=8686,exports.ids=[8686],exports.modules={21971:()=>{},32002:(e,i,a)=>{Promise.resolve().then(a.t.bind(a,69355,23)),Promise.resolve().then(a.t.bind(a,54439,23)),Promise.resolve().then(a.t.bind(a,67851,23)),Promise.resolve().then(a.t.bind(a,94730,23)),Promise.resolve().then(a.t.bind(a,19774,23)),Promise.resolve().then(a.t.bind(a,53170,23)),Promise.resolve().then(a.t.bind(a,20968,23)),Promise.resolve().then(a.t.bind(a,78298,23))},34356:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>c,metadata:()=>o,viewport:()=>t});var r=a(38828),s=a(7666),n=a.n(s);a(21971);let t={width:"device-width",initialScale:1},o={metadataBase:new URL("https://portal.chia-cundinamarca.gov.co"),title:{default:"CHIA - Portal Ciudadano Digital",template:"%s | CHIA - Portal Ciudadano"},description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Realiza tr\xe1mites en l\xednea, consulta informaci\xf3n municipal y accede a servicios digitales las 24 horas.",keywords:["Ch\xeda","Cundinamarca","servicios ciudadanos","gobierno digital","tr\xe1mites en l\xednea","certificados","impuestos","licencias","portal ciudadano","alcald\xeda","municipio"],authors:[{name:"Alcald\xeda Municipal de Ch\xeda"}],creator:"Alcald\xeda Municipal de Ch\xeda",publisher:"Alcald\xeda Municipal de Ch\xeda",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"es_CO",url:"https://portal.chia-cundinamarca.gov.co",siteName:"CHIA - Portal Ciudadano",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Tr\xe1mites en l\xednea las 24 horas.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Portal Ciudadano de Ch\xeda"}]},twitter:{card:"summary_large_image",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca.",images:["/og-image.jpg"],creator:"@AlcaldiaChia"},alternates:{canonical:"https://portal.chia-cundinamarca.gov.co"},category:"government"};function c({children:e}){return(0,r.jsx)("html",{lang:"es",className:"h-full",children:(0,r.jsx)("body",{className:`${n().className} h-full`,children:(0,r.jsx)("div",{id:"root",className:"min-h-full",children:e})})})}},50154:(e,i,a)=>{Promise.resolve().then(a.t.bind(a,30385,23)),Promise.resolve().then(a.t.bind(a,33737,23)),Promise.resolve().then(a.t.bind(a,86081,23)),Promise.resolve().then(a.t.bind(a,1904,23)),Promise.resolve().then(a.t.bind(a,35856,23)),Promise.resolve().then(a.t.bind(a,55492,23)),Promise.resolve().then(a.t.bind(a,89082,23)),Promise.resolve().then(a.t.bind(a,45812,23))},55362:()=>{},68713:(e,i,a)=>{"use strict";a.d(i,{A:()=>C});var r=a(13486),s=a(60159),n=a(49989),t=a.n(n),o=a(2984),c=a(58769),l=a(272),d=a(24677),m=a(766),h=a(86604),u=a(48727),f=a(54575),g=a(55938),p=a(95288),x=a(61817),y=a(38149),v=a(58229),b=a(32399),j=a(57469);let N=[{name:"Inicio",href:"/",icon:c.A},{name:"Dependencias",href:"/dependencias",icon:l.A},{name:"Tr\xe1mites",href:"/tramites",icon:d.A},{name:"OPAs",href:"/opas",icon:m.A},{name:"Servicios",href:"/servicios",icon:h.A,children:[{name:"Certificados",href:"/servicios/certificados",description:"Certificados de residencia, nacimiento, etc."},{name:"Pagos en L\xednea",href:"/servicios/pagos",description:"Impuestos, multas y tasas municipales"},{name:"Licencias",href:"/servicios/licencias",description:"Construcci\xf3n, funcionamiento, etc."},{name:"Registro Civil",href:"/servicios/registro",description:"Documentos de identidad y registro"},{name:"Consultas",href:"/servicios/consultas",description:"Informaci\xf3n catastral y municipal"}]},{name:"Asistente IA",href:"/chat",icon:u.A},{name:"Preguntas Frecuentes",href:"/preguntas-frecuentes",icon:f.A},{name:"Contacto",href:"/contacto",icon:g.A},{name:"Acerca de",href:"/acerca",icon:p.A}];function C({onSearchClick:e,onChatClick:i}){let[a,n]=(0,s.useState)(!1),[c,l]=(0,s.useState)(null),d=(0,o.usePathname)(),m=(0,s.useRef)(null),h=(e,i)=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),i())},u=e=>{l(c===e?null:e)},f=e=>"/"===e?"/"===d:d.startsWith(e);return(0,r.jsx)("nav",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40",role:"navigation","aria-label":"Navegaci\xf3n principal",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(t(),{href:"/",className:"flex items-center gap-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg p-1","aria-label":"Ir al inicio - Portal Ciudadano de Ch\xeda",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"C"})}),(0,r.jsxs)("div",{className:"hidden sm:block",children:[(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"CHIA"}),(0,r.jsx)("span",{className:"text-sm text-gray-500 block leading-none",children:"Portal Ciudadano"})]})]})}),(0,r.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:N.map(e=>(0,r.jsx)("div",{className:"relative",ref:e.children?m:void 0,children:e.children?(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>u(e.name),onKeyDown:i=>h(i,()=>u(e.name)),className:`flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${f(e.href)?"text-primary-600 bg-primary-50":"text-gray-700 hover:text-primary-600 hover:bg-gray-50"}`,"aria-expanded":c===e.name,"aria-haspopup":"true",children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4"}),e.name,(0,r.jsx)(x.A,{className:`h-4 w-4 transition-transform ${c===e.name?"rotate-180":""}`})]}),c===e.name&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-1 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:e.children.map(e=>(0,r.jsxs)(t(),{href:e.href,className:`block px-4 py-3 text-sm hover:bg-gray-50 transition-colors focus:outline-none focus:bg-gray-50 ${f(e.href)?"text-primary-600 bg-primary-50":"text-gray-700"}`,onClick:()=>l(null),children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),e.description&&(0,r.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:e.description})]},e.name))})]}):(0,r.jsxs)(t(),{href:e.href,className:`flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${f(e.href)?"text-primary-600 bg-primary-50":"text-gray-700 hover:text-primary-600 hover:bg-gray-50"}`,children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4"}),e.name]})},e.name))}),(0,r.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:e,className:"p-2 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2","aria-label":"Buscar servicios",children:(0,r.jsx)(y.A,{className:"h-5 w-5"})}),(0,r.jsxs)(t(),{href:"/auth/login",className:"flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),"Ingresar"]})]}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)("button",{onClick:()=>n(!a),className:"p-2 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2","aria-label":a?"Cerrar men\xfa":"Abrir men\xfa","aria-expanded":a,children:a?(0,r.jsx)(b.A,{className:"h-6 w-6"}):(0,r.jsx)(j.A,{className:"h-6 w-6"})})})]}),a&&(0,r.jsxs)("div",{className:"md:hidden border-t border-gray-200 py-4",children:[(0,r.jsx)("div",{className:"space-y-1",children:N.map(e=>(0,r.jsx)("div",{children:e.children?(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>u(e.name),className:`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${f(e.href)?"text-primary-600 bg-primary-50":"text-gray-700 hover:text-primary-600 hover:bg-gray-50"}`,children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4"}),e.name]}),(0,r.jsx)(x.A,{className:`h-4 w-4 transition-transform ${c===e.name?"rotate-180":""}`})]}),c===e.name&&(0,r.jsx)("div",{className:"mt-1 ml-6 space-y-1",children:e.children.map(e=>(0,r.jsx)(t(),{href:e.href,className:`block px-3 py-2 rounded-lg text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${f(e.href)?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-primary-600 hover:bg-gray-50"}`,children:e.name},e.name))})]}):(0,r.jsxs)(t(),{href:e.href,className:`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${f(e.href)?"text-primary-600 bg-primary-50":"text-gray-700 hover:text-primary-600 hover:bg-gray-50"}`,children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4"}),e.name]})},e.name))}),(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200 space-y-2",children:[(0,r.jsxs)("button",{onClick:e,className:"w-full flex items-center gap-2 px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),"Buscar Servicios"]}),(0,r.jsxs)(t(),{href:"/auth/login",className:"w-full flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),"Iniciar Sesi\xf3n"]})]})]})]})})}},73514:()=>{}};