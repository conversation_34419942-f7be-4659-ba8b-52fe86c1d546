'use client';

import { useState, useCallback, useRef } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface ServicesSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  className?: string;
}

export default function ServicesSearch({
  onSearch,
  placeholder = "Buscar servicios...",
  className = ""
}: ServicesSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Simple debounce implementation
  const debouncedSearch = useCallback((query: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      onSearch(query);
    }, 300);
  }, [onSearch]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedSearch(value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  return (
    <form onSubmit={handleSubmit} className={className}>
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="text"
          value={searchQuery}
          onChange={handleInputChange}
          placeholder={placeholder}
          className="w-full pl-12 pr-4 py-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent"
        />
      </div>
    </form>
  );
}
