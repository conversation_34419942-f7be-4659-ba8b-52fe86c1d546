(()=>{var e={};e.id=8315,e.ids=[8315],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13484:(e,t,r)=>{Promise.resolve().then(r.bind(r,62257)),Promise.resolve().then(r.t.bind(r,42671,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22614:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>d,tree:()=>c});var s=r(24332),o=r(48819),i=r(67851),n=r.n(i),a=r(97540),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c={children:["",{children:["(auth)",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36586)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,86580)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\(auth)\\auth\\login\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(auth)/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},26928:(e,t,r)=>{"use strict";var s,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>N,createClientComponentClient:()=>u,createMiddlewareClient:()=>x,createMiddlewareSupabaseClient:()=>O,createPagesBrowserClient:()=>p,createPagesServerClient:()=>f,createRouteHandlerClient:()=>w,createServerActionClient:()=>S,createServerComponentClient:()=>y,createServerSupabaseClient:()=>A}),e.exports=((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of n(t))a.call(e,r)||void 0===r||o(e,r,{get:()=>t[r],enumerable:!(s=i(t,r))||s.enumerable});return e})(o({},"__esModule",{value:!0}),l);var c=r(33988);function u({supabaseUrl:e="https://hndowofzjzjoljnapokv.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY",options:r,cookieOptions:o,isSingleton:i=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let n=()=>{var s;return(0,c.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(s=null==r?void 0:r.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new c.BrowserCookieAuthStorageAdapter(o)}})};if(i){let e=s??n();return"undefined"==typeof window?e:(s||(s=e),s)}return n()}var p=u,d=r(33988),h=r(64341),m=class extends d.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,s;return(0,h.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,d.parseCookies)(t)[e]).find(e=>!!e)??(null==(s=this.context.req)?void 0:s.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var s;let o=(0,h.splitCookiesString)((null==(s=this.context.res.getHeader("set-cookie"))?void 0:s.toString())??"").filter(t=>!(e in(0,d.parseCookies)(t))),i=(0,d.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...o,i])}};function f(e,{supabaseUrl:t="https://hndowofzjzjoljnapokv.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY",options:s,cookieOptions:o}={}){var i;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,d.createSupabaseClient)(t,r,{...s,global:{...null==s?void 0:s.global,headers:{...null==(i=null==s?void 0:s.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new m(e,o)}})}var b=r(33988),g=r(64341),v=class extends b.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;let r=(0,g.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,b.parseCookies)(t)[e]).find(e=>!!e);return r||(0,b.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let s=(0,b.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",s)}};function x(e,{supabaseUrl:t="https://hndowofzjzjoljnapokv.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY",options:s,cookieOptions:o}={}){var i;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,b.createSupabaseClient)(t,r,{...s,global:{...null==s?void 0:s.global,headers:{...null==(i=null==s?void 0:s.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new v(e,o)}})}var C=r(33988),I=class extends C.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function y(e,{supabaseUrl:t="https://hndowofzjzjoljnapokv.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY",options:s,cookieOptions:o}={}){var i;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,C.createSupabaseClient)(t,r,{...s,global:{...null==s?void 0:s.global,headers:{...null==(i=null==s?void 0:s.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new I(e,o)}})}var k=r(33988),j=class extends k.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function w(e,{supabaseUrl:t="https://hndowofzjzjoljnapokv.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY",options:s,cookieOptions:o}={}){var i;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,k.createSupabaseClient)(t,r,{...s,global:{...null==s?void 0:s.global,headers:{...null==(i=null==s?void 0:s.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new j(e,o)}})}var S=w;function N({supabaseUrl:e="https://hndowofzjzjoljnapokv.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY",options:r,cookieOptions:s}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),p({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:s})}function A(e,{supabaseUrl:t="https://hndowofzjzjoljnapokv.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY",options:s,cookieOptions:o}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),f(e,{supabaseUrl:t,supabaseKey:r,options:s,cookieOptions:o})}function O(e,{supabaseUrl:t="https://hndowofzjzjoljnapokv.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY",options:s,cookieOptions:o}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),x(e,{supabaseUrl:t,supabaseKey:r,options:s,cookieOptions:o})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>j,CookieAuthStorageAdapter:()=>k,DEFAULT_COOKIE_OPTIONS:()=>I,createSupabaseClient:()=>w,isBrowser:()=>C,parseCookies:()=>S,parseSupabaseCookie:()=>v,serializeCookie:()=>N,stringifySupabaseSession:()=>x});var s=r(79428);new TextEncoder;let o=new TextDecoder;s.Buffer.isEncoding("base64url");let i=e=>s.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=o.decode(t)),t}(e),"base64");var n=r(78126),a=Object.create,l=Object.defineProperty,c=Object.getOwnPropertyDescriptor,u=Object.getOwnPropertyNames,p=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,h=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of u(t))d.call(e,o)||o===r||l(e,o,{get:()=>t[o],enumerable:!(s=c(t,o))||s.enumerable});return e},m=(e,t,r)=>(r=null!=e?a(p(e)):{},h(!t&&e&&e.__esModule?r:l(r,"default",{value:e,enumerable:!0}),e)),f=((e,t)=>function(){return t||(0,e[u(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},o=(t||{}).decode||s,i=0;i<e.length;){var n=e.indexOf("=",i);if(-1===n)break;var a=e.indexOf(";",i);if(-1===a)a=e.length;else if(a<n){i=e.lastIndexOf(";",n-1)+1;continue}var l=e.slice(i,n).trim();if(void 0===r[l]){var c=e.slice(n+1,a).trim();34===c.charCodeAt(0)&&(c=c.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(c,o)}i=a+1}return r},e.serialize=function(e,s,i){var n=i||{},a=n.encode||o;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=a(s);if(l&&!r.test(l))throw TypeError("argument val is invalid");var c=e+"="+l;if(null!=n.maxAge){var u=n.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(u)}if(n.domain){if(!r.test(n.domain))throw TypeError("option domain is invalid");c+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError("option path is invalid");c+="; Path="+n.path}if(n.expires){var p,d=n.expires;if(p=d,"[object Date]"!==t.call(p)&&!(p instanceof Date)||isNaN(d.valueOf()))throw TypeError("option expires is invalid");c+="; Expires="+d.toUTCString()}if(n.httpOnly&&(c+="; HttpOnly"),n.secure&&(c+="; Secure"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():n.priority){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function s(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function o(e){return encodeURIComponent(e)}}}),b=m(f()),g=m(f());function v(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,s,o]=t[0].split("."),n=i(s),a=new TextDecoder,{exp:l,sub:c,...u}=JSON.parse(a.decode(n));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:c,factors:t[4],...u}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function x(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function C(){return"undefined"!=typeof window&&void 0!==window.document}var I={path:"/",sameSite:"lax",maxAge:31536e6},y=RegExp(".{1,3180}","g"),k=class{constructor(e){this.cookieOptions={...I,...e,maxAge:I.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(v(t));let r=function(e,t=()=>null){let r=[];for(let s=0;;s++){let o=t(`${e}.${s}`);if(!o)break;r.push(o)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(v(r)):null}setItem(e,t){if(e.endsWith("-code-verifier"))return void this.setCookie(e,t);(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let s=[],o=t.match(y);return null==o||o.forEach((t,r)=>{let o=`${e}.${r}`;s.push({name:o,value:t})}),s})(e,x(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},j=class extends k{constructor(e){super(e)}getCookie(e){return C()?(0,b.parse)(document.cookie)[e]:null}setCookie(e,t){if(!C())return null;document.cookie=(0,b.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!C())return null;document.cookie=(0,b.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function w(e,t,r){var s;let o=C();return(0,n.UU)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:o,detectSessionInUrl:o,persistSession:!0,storage:r.auth.storage,...(null==(s=r.auth)?void 0:s.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var S=g.parse,N=g.serialize},34631:e=>{"use strict";e.exports=require("tls")},36586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(38828),o=r(42671),i=r.n(o),n=r(62257);function a(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Iniciar Sesi\xf3n"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Accede a tu cuenta del portal ciudadano"})]}),(0,s.jsx)(n.LoginForm,{}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["\xbfNo tienes una cuenta?"," ",(0,s.jsx)(i(),{href:"/auth/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"Reg\xedstrate aqu\xed"})]})})]})})}},42671:(e,t,r)=>{let{createProxy:s}=r(47927);e.exports=s("C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\node_modules\\next\\dist\\client\\app-dir\\link.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56682:(e,t,r)=>{"use strict";r.d(t,{LoginForm:()=>p});var s=r(13486),o=r(60159),i=r(2984),n=r(78126),a=r(26928);let l="https://hndowofzjzjoljnapokv.supabase.co",c="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(!l||!c)throw Error("Missing Supabase environment variables");let u=()=>(0,a.createClientComponentClient)();function p(){let[e,t]=(0,o.useState)(""),[r,n]=(0,o.useState)(""),[a,l]=(0,o.useState)(!1),[c,p]=(0,o.useState)(null),d=(0,i.useRouter)(),h=u(),m=async t=>{t.preventDefault(),l(!0),p(null);try{let{data:t,error:s}=await h.auth.signInWithPassword({email:e,password:r});if(s)return void p(s.message);t.user&&(d.push("/dashboard"),d.refresh())}catch(e){p("Error inesperado. Por favor intenta de nuevo."),console.error("Login error:",e)}finally{l(!1)}},f=async()=>{l(!0),p(null);try{let{error:e}=await h.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&p(e.message)}catch(e){p("Error inesperado. Por favor intenta de nuevo."),console.error("Google login error:",e)}finally{l(!1)}};return(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:m,children:[c&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,s.jsx)("div",{className:"text-sm text-red-700",children:c})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"form-label",children:"Correo Electr\xf3nico"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"form-input",value:e,onChange:e=>t(e.target.value),disabled:a})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"form-label",children:"Contrase\xf1a"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"form-input",value:r,onChange:e=>n(e.target.value),disabled:a})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{type:"submit",disabled:a,className:"btn-primary w-full",children:a?"Iniciando sesi\xf3n...":"Iniciar Sesi\xf3n"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-gray-50 text-gray-500",children:"O contin\xfaa con"})})]}),(0,s.jsxs)("button",{type:"button",onClick:f,disabled:a,className:"btn-secondary w-full flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]})]})]})}(0,n.UU)(l,c)},62257:(e,t,r)=>{"use strict";r.d(t,{LoginForm:()=>s});let s=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\components\\auth\\LoginForm.tsx","LoginForm")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64341:e=>{"use strict";var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function s(e,s){var o,i,n,a,l=e.split(";").filter(r),c=(o=l.shift(),i="",n="",(a=o.split("=")).length>1?(i=a.shift(),n=a.join("=")):n=o,{name:i,value:n}),u=c.name,p=c.value;s=s?Object.assign({},t,s):t;try{p=s.decodeValues?decodeURIComponent(p):p}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+p+"'. Set options.decodeValues to false to disable this feature.",e)}var d={name:u,value:p};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),s=t.join("=");"expires"===r?d.expires=new Date(s):"max-age"===r?d.maxAge=parseInt(s,10):"secure"===r?d.secure=!0:"httponly"===r?d.httpOnly=!0:"samesite"===r?d.sameSite=s:"partitioned"===r?d.partitioned=!0:d[r]=s}),d}function o(e,o){if(o=o?Object.assign({},t,o):t,!e)if(!o.map)return[];else return{};if(e.headers)if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var i=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];i||!e.headers.cookie||o.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=i}return(Array.isArray(e)||(e=[e]),o.map)?e.filter(r).reduce(function(e,t){var r=s(t,o);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return s(e,o)})}e.exports=o,e.exports.parse=o,e.exports.parseString=s,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,s,o,i,n=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,i=!1;l();)if(","===(r=e.charAt(a))){for(s=a,a+=1,l(),o=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(i=!0,a=o,n.push(e.substring(t,s)),t=a):a=s+1}else a+=1;(!i||a>=e.length)&&n.push(e.substring(t,e.length))}return n}},74075:e=>{"use strict";e.exports=require("zlib")},77036:(e,t,r)=>{Promise.resolve().then(r.bind(r,56682)),Promise.resolve().then(r.t.bind(r,49989,23))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[191,7118,114,1085,5900],()=>r(22614));module.exports=s})();