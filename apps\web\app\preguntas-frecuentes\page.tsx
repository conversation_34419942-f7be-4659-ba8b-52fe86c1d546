'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import PageLayout from '@/components/layout/PageLayout';
import { 
  MagnifyingGlassIcon, 
  ChevronDownIcon, 
  ChevronUpIcon,
  ArrowRightIcon,
  QuestionMarkCircleIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface FAQ {
  id: string;
  tema: string;
  pregunta: string;
  respuesta: string;
  dependencia: {
    id: string;
    codigo: string;
    nombre: string;
    sigla: string;
  };
}

interface FAQResponse {
  success: boolean;
  data: FAQ[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
  };
}

export default function PreguntasFrecuentesPage() {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTema, setSelectedTema] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [temas, setTemas] = useState<string[]>([]);

  // Fetch FAQs
  const fetchFAQs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (selectedTema) params.append('tema', selectedTema);
      params.append('limit', '50'); // Show more FAQs

      const response = await fetch(`/api/faqs?${params}`);
      if (!response.ok) {
        throw new Error('Error al cargar las preguntas frecuentes');
      }

      const data: FAQResponse = await response.json();
      setFaqs(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  // Fetch unique topics for filtering
  const fetchTemas = async () => {
    try {
      const uniqueTemas = [...new Set(faqs.map(faq => faq.tema))].filter(Boolean);
      setTemas(uniqueTemas.sort());
    } catch (err) {
      console.error('Error fetching topics:', err);
    }
  };

  useEffect(() => {
    fetchFAQs();
  }, [searchQuery, selectedTema]);

  useEffect(() => {
    if (faqs.length > 0) {
      fetchTemas();
    }
  }, [faqs]);

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchFAQs();
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedTema('');
    setExpandedFAQ(null);
  };

  return (
    <PageLayout className="bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link href="/" className="text-gray-400 hover:text-gray-500">
                  Inicio
                </Link>
              </li>
              <li>
                <ArrowRightIcon className="h-4 w-4 text-gray-400" />
              </li>
              <li>
                <span className="text-gray-900 font-medium">Preguntas Frecuentes</span>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <QuestionMarkCircleIcon className="h-16 w-16 text-primary-600 mx-auto mb-6" />
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Preguntas Frecuentes
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Encuentra respuestas rápidas a las consultas más comunes sobre los servicios 
              municipales de Chía. Si no encuentras lo que buscas, puedes contactarnos directamente.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search and Filters */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search Input */}
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="Buscar en preguntas y respuestas..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>

              {/* Topic Filter */}
              <div className="lg:w-80">
                <div className="relative">
                  <FunnelIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <select
                    value={selectedTema}
                    onChange={(e) => setSelectedTema(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 appearance-none bg-white"
                  >
                    <option value="">Todos los temas</option>
                    {temas.map((tema) => (
                      <option key={tema} value={tema}>
                        {tema}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Clear Filters Button */}
              {(searchQuery || selectedTema) && (
                <button
                  type="button"
                  onClick={clearFilters}
                  className="px-4 py-3 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Limpiar
                </button>
              )}
            </div>
          </form>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Cargando preguntas frecuentes...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* FAQs List */}
        {!loading && !error && (
          <div className="space-y-4">
            {faqs.length === 0 ? (
              <div className="text-center py-12 bg-white rounded-xl shadow-md">
                <QuestionMarkCircleIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No se encontraron preguntas frecuentes
                </h3>
                <p className="text-gray-600 mb-6">
                  Intenta con otros términos de búsqueda o selecciona un tema diferente.
                </p>
                <button
                  onClick={clearFilters}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  Ver todas las preguntas
                </button>
              </div>
            ) : (
              <>
                <div className="mb-6">
                  <p className="text-gray-600">
                    Mostrando {faqs.length} pregunta{faqs.length !== 1 ? 's' : ''} frecuente{faqs.length !== 1 ? 's' : ''}
                    {searchQuery && ` para "${searchQuery}"`}
                    {selectedTema && ` en el tema "${selectedTema}"`}
                  </p>
                </div>

                {faqs.map((faq) => (
                  <div key={faq.id} className="bg-white rounded-xl shadow-md overflow-hidden">
                    <button
                      onClick={() => toggleFAQ(faq.id)}
                      className="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            {faq.pregunta}
                          </h3>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded-full">
                              {faq.tema}
                            </span>
                            <span>
                              {faq.dependencia.nombre}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          {expandedFAQ === faq.id ? (
                            <ChevronUpIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </button>

                    {expandedFAQ === faq.id && (
                      <div className="px-6 pb-6 border-t border-gray-100">
                        <div className="pt-4">
                          <div className="prose max-w-none text-gray-700">
                            {faq.respuesta.split('\n').map((paragraph, index) => (
                              <p key={index} className="mb-3 last:mb-0">
                                {paragraph}
                              </p>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </>
            )}
          </div>
        )}

        {/* Help Section */}
        <div className="mt-12 bg-white rounded-xl shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            ¿No encontraste lo que buscabas?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Nuestro asistente de IA puede ayudarte con consultas específicas, 
            o puedes contactarnos directamente a través de nuestros canales oficiales.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/chat"
              className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              Hablar con el Asistente IA
            </Link>
            <Link
              href="/contacto"
              className="inline-flex items-center gap-2 border border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Contactar Soporte
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
