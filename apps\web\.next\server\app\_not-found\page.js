(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21971:()=>{},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32002:(e,i,a)=>{Promise.resolve().then(a.t.bind(a,69355,23)),Promise.resolve().then(a.t.bind(a,54439,23)),Promise.resolve().then(a.t.bind(a,67851,23)),Promise.resolve().then(a.t.bind(a,94730,23)),Promise.resolve().then(a.t.bind(a,19774,23)),Promise.resolve().then(a.t.bind(a,53170,23)),Promise.resolve().then(a.t.bind(a,20968,23)),Promise.resolve().then(a.t.bind(a,78298,23))},33873:e=>{"use strict";e.exports=require("path")},34356:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>d,metadata:()=>s,viewport:()=>o});var r=a(38828),t=a(7666),n=a.n(t);a(21971);let o={width:"device-width",initialScale:1},s={metadataBase:new URL("https://portal.chia-cundinamarca.gov.co"),title:{default:"CHIA - Portal Ciudadano Digital",template:"%s | CHIA - Portal Ciudadano"},description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Realiza tr\xe1mites en l\xednea, consulta informaci\xf3n municipal y accede a servicios digitales las 24 horas.",keywords:["Ch\xeda","Cundinamarca","servicios ciudadanos","gobierno digital","tr\xe1mites en l\xednea","certificados","impuestos","licencias","portal ciudadano","alcald\xeda","municipio"],authors:[{name:"Alcald\xeda Municipal de Ch\xeda"}],creator:"Alcald\xeda Municipal de Ch\xeda",publisher:"Alcald\xeda Municipal de Ch\xeda",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"es_CO",url:"https://portal.chia-cundinamarca.gov.co",siteName:"CHIA - Portal Ciudadano",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca. Tr\xe1mites en l\xednea las 24 horas.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Portal Ciudadano de Ch\xeda"}]},twitter:{card:"summary_large_image",title:"CHIA - Portal Ciudadano Digital",description:"Portal oficial de servicios ciudadanos para el municipio de Ch\xeda, Cundinamarca.",images:["/og-image.jpg"],creator:"@AlcaldiaChia"},alternates:{canonical:"https://portal.chia-cundinamarca.gov.co"},category:"government"};function d({children:e}){return(0,r.jsx)("html",{lang:"es",className:"h-full",children:(0,r.jsx)("body",{className:`${n().className} h-full`,children:(0,r.jsx)("div",{id:"root",className:"min-h-full",children:e})})})}},50154:(e,i,a)=>{Promise.resolve().then(a.t.bind(a,30385,23)),Promise.resolve().then(a.t.bind(a,33737,23)),Promise.resolve().then(a.t.bind(a,86081,23)),Promise.resolve().then(a.t.bind(a,1904,23)),Promise.resolve().then(a.t.bind(a,35856,23)),Promise.resolve().then(a.t.bind(a,55492,23)),Promise.resolve().then(a.t.bind(a,89082,23)),Promise.resolve().then(a.t.bind(a,45812,23))},55362:()=>{},56732:(e,i,a)=>{"use strict";a.r(i),a.d(i,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=a(24332),t=a(48819),n=a(67851),o=a.n(n),s=a(97540),d={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);a.d(i,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.t.bind(a,19033,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73514:()=>{}};var i=require("../../webpack-runtime.js");i.C(e);var a=e=>i(i.s=e),r=i.X(0,[191,7118],()=>a(56732));module.exports=r})();