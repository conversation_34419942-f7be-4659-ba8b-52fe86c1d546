{"/_not-found/page": "app/_not-found/page.js", "/opas/page": "app/opas/page.js", "/page": "app/page.js", "/(auth)/auth/callback/route": "app/(auth)/auth/callback/route.js", "/api/dependencias/[id]/route": "app/api/dependencias/[id]/route.js", "/api/dependencias/[id]/subdependencias/route": "app/api/dependencias/[id]/subdependencias/route.js", "/api/dependencias/route": "app/api/dependencias/route.js", "/api/faqs/[id]/route": "app/api/faqs/[id]/route.js", "/api/faqs/route": "app/api/faqs/route.js", "/api/faqs/temas/route": "app/api/faqs/temas/route.js", "/api/health/route": "app/api/health/route.js", "/api/opas/[id]/route": "app/api/opas/[id]/route.js", "/api/opas/categorias/route": "app/api/opas/categorias/route.js", "/api/opas/route": "app/api/opas/route.js", "/api/search/route": "app/api/search/route.js", "/api/tramites/[id]/route": "app/api/tramites/[id]/route.js", "/api/tramites/categorias/route": "app/api/tramites/categorias/route.js", "/api/tramites/route": "app/api/tramites/route.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js", "/dependencias/[id]/page": "app/dependencias/[id]/page.js", "/dependencias/page": "app/dependencias/page.js", "/opas/[id]/page": "app/opas/[id]/page.js", "/preguntas-frecuentes/page": "app/preguntas-frecuentes/page.js", "/tramites/[id]/page": "app/tramites/[id]/page.js", "/tramites/page": "app/tramites/page.js", "/chat/page": "app/chat/page.js", "/acerca/page": "app/acerca/page.js", "/contacto/page": "app/contacto/page.js", "/servicios/certificados/page": "app/servicios/certificados/page.js", "/servicios/consultas/page": "app/servicios/consultas/page.js", "/servicios/page": "app/servicios/page.js", "/servicios/licencias/page": "app/servicios/licencias/page.js", "/servicios/registro/page": "app/servicios/registro/page.js", "/servicios/pagos/page": "app/servicios/pagos/page.js", "/admin/faqs/page": "app/admin/faqs/page.js", "/admin/opas/page": "app/admin/opas/page.js", "/admin/page": "app/admin/page.js", "/admin/tramites/nuevo/page": "app/admin/tramites/nuevo/page.js", "/admin/tramites/page": "app/admin/tramites/page.js", "/(auth)/auth/login/page": "app/(auth)/auth/login/page.js", "/(auth)/verify-email/page": "app/(auth)/verify-email/page.js", "/(auth)/login/page": "app/(auth)/login/page.js"}