(()=>{var e={};e.id=4349,e.ids=[4349],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},40585:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>c});var a=t(48106),i=t(48819),n=t(12050),o=t(4235),u=t(73474);async function p(e){try{let r=(0,u.y8)(),{searchParams:t}=new URL(e.url),s=parseInt(t.get("limit")||"20"),a=parseInt(t.get("offset")||"0"),i=t.get("tema"),n=t.get("dependencia"),p=t.get("search"),c=r.from("faqs_view").select("*").order("tema").range(a,a+s-1);i&&(c=c.ilike("tema",`%${i}%`)),n&&(c=c.eq("dependencia_id",n)),p&&(c=c.or(`pregunta.ilike.%${p}%,respuesta.ilike.%${p}%`));let{data:d,error:l,count:m}=await c;if(l)return console.error("Error fetching FAQs:",l),o.NextResponse.json({error:"Failed to fetch FAQs",details:l.message},{status:500});let f=d?.map(e=>({id:e.id,tema:e.tema,pregunta:e.pregunta,respuesta:e.respuesta,palabrasClave:[],prioridad:0,vistas:0,utilidad:0,dependencia:{id:e.dependencia_id,codigo:e.dependencia_codigo,nombre:e.dependencia_nombre,sigla:e.dependencia_sigla}}))||[];return o.NextResponse.json({success:!0,data:f,pagination:{limit:s,offset:a,total:m||f.length},filters:{tema:i,dependencia:n,search:p}})}catch(e){return console.error("Unexpected error in FAQs API:",e),o.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function c(e){try{let r=await e.json(),{action:t}=r,s=(0,u.y8)();if("get_topics"===t){let{data:e,error:r}=await s.from("faqs_view").select("tema").not("tema","is",null);if(r)throw r;let t=[...new Set(e?.map(e=>e.tema))].filter(Boolean);return o.NextResponse.json({success:!0,data:t})}if("increment_views"===t){let{faqId:e}=r,{data:t}=await s.schema("ingestion").from("faqs").select("vistas").eq("id",e).single(),a=(t?.vistas||0)+1,{error:i}=await s.schema("ingestion").from("faqs").update({vistas:a}).eq("id",e);if(i)throw i;return o.NextResponse.json({success:!0,message:"Views incremented"})}if(!t){let{data:{user:e},error:t}=await s.auth.getUser();if(t||!e)return o.NextResponse.json({error:"No autorizado"},{status:401});let{data:a}=await s.rpc("check_user_permission",{p_user_id:e.id,p_action:"INSERT",p_table_name:"faqs"});if(!a)return o.NextResponse.json({error:"Sin permisos para crear FAQs"},{status:403});let{tema:i,pregunta:n,respuesta:u,palabras_clave:p,prioridad:c,subdependencia_id:d,metadata:l}=r;if(!i||!n||!u||!d)return o.NextResponse.json({error:"Tema, pregunta, respuesta y subdependencia son requeridos"},{status:400});let{data:m,error:f}=await s.schema("ingestion").from("faqs").insert({tema:i,pregunta:n,respuesta:u,palabras_clave:p,prioridad:c||0,vistas:0,subdependencia_id:d,metadata:l,activo:!0,fuente_original:"portal_admin"}).select().single();if(f)return console.error("Error creating FAQ:",f),o.NextResponse.json({error:"Error al crear FAQ"},{status:500});return o.NextResponse.json({success:!0,data:m})}return o.NextResponse.json({error:"Acci\xf3n no v\xe1lida"},{status:400})}catch(e){return console.error("Error in FAQs POST:",e),o.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/faqs/route",pathname:"/api/faqs",filename:"route",bundlePath:"app/api/faqs/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\faqs\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:f}=d;function x(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,r,t)=>{"use strict";t.d(r,{y8:()=>n});var s=t(2492);let a="https://hndowofzjzjoljnapokv.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(process.env.SUPABASE_SERVICE_ROLE_KEY,!a||!i)throw Error("Missing Supabase environment variables");function n(){return(0,s.UU)(a,i)}n()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744],()=>t(40585));module.exports=s})();