# Estrategia de Testing - Sistema CHIA

## Visión General

Esta documentación describe la estrategia integral de testing implementada para el sistema CHIA, incluyendo pruebas unitarias, de integración, end-to-end, y validación de métricas.

## Tipos de Pruebas

### 1. Pruebas Unitarias

**Ubicación**: `tests/api/`, `tests/components/`, `tests/lib/`

**Herramientas**:
- Jest como framework principal
- React Testing Library para componentes
- Mocks para dependencias externas

**Cobertura**:
- APIs de trámites, OPAs y FAQs
- Componentes de administración
- Utilidades y helpers
- Validaciones de formularios

**Ejemplo de estructura**:
```typescript
describe('Trámites API', () => {
  it('should return tramites list successfully', async () => {
    // Test implementation
  });
  
  it('should handle search parameter correctly', async () => {
    // Test implementation
  });
});
```

### 2. Pruebas de Integración

**Ubicación**: `tests/integration/`

**Enfoque**:
- Navegación jerárquica completa
- Flujos de datos entre componentes
- Integración con APIs de Supabase
- Validación de métricas y conteos

**Casos clave**:
- Navegación Dependencias → Subdependencias → Contenido
- Filtrado y búsqueda en múltiples niveles
- Consistencia de métricas agregadas
- Manejo de errores en cadena

### 3. Pruebas de Componentes

**Ubicación**: `tests/components/admin/`

**Cobertura**:
- Layout de administración
- Formularios CRUD
- Tablas con filtros y paginación
- Estados de carga y error
- Responsividad y accesibilidad

**Patrones de testing**:
```typescript
describe('AdminLayout', () => {
  it('renders admin layout with sidebar navigation', () => {
    render(<AdminLayout><div>Test</div></AdminLayout>);
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });
});
```

### 4. Validación de Métricas

**Script**: `scripts/validate-metrics.ts`

**Funcionalidades**:
- Validación de consistencia de conteos
- Verificación de agregaciones jerárquicas
- Detección de discrepancias en métricas
- Reporte detallado de resultados

**Ejecución**:
```bash
npm run validate-metrics
```

## Configuración de Testing

### Jest Configuration

**Archivo**: `jest.config.js`

```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/apps/web/$1'
  },
  collectCoverageFrom: [
    'apps/web/**/*.{ts,tsx}',
    '!apps/web/**/*.d.ts',
    '!apps/web/node_modules/**'
  ]
};
```

### Setup File

**Archivo**: `jest.setup.js`

```javascript
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    replace: jest.fn()
  }),
  usePathname: () => '/admin'
}));
```

## Estrategias de Mocking

### 1. APIs de Supabase

```typescript
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn().mockReturnValue({
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    // ... otros métodos
  })
}));
```

### 2. Fetch API

```typescript
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

mockFetch.mockResolvedValueOnce({
  ok: true,
  json: async () => ({ success: true, data: [] })
} as Response);
```

### 3. Next.js Components

```typescript
jest.mock('next/link', () => {
  return ({ children, href, ...props }: any) => (
    <a href={href} {...props}>{children}</a>
  );
});
```

## Casos de Prueba Críticos

### 1. Navegación Jerárquica

```typescript
it('should navigate from dependencies to subdependencies to content', async () => {
  // Mock API responses for each level
  // Test complete navigation flow
  // Verify data consistency
});
```

### 2. Filtrado y Búsqueda

```typescript
it('should filter tramites by dependency and category', async () => {
  // Mock filtered API response
  // Test filter application
  // Verify results match criteria
});
```

### 3. Gestión de Estados

```typescript
it('should handle loading and error states correctly', async () => {
  // Test loading state display
  // Test error handling
  // Test success state transition
});
```

### 4. Formularios CRUD

```typescript
it('should create tramite successfully', async () => {
  // Mock form submission
  // Test validation
  // Verify API call with correct data
});
```

## Métricas de Calidad

### Cobertura de Código

**Objetivo**: Mínimo 80% para código crítico

**Áreas críticas**:
- APIs de datos (trámites, OPAs, FAQs)
- Componentes de administración
- Validaciones y formularios
- Navegación jerárquica

### Criterios de Aceptación

**Para cada funcionalidad**:
1. ✅ Al menos 1 prueba de caso exitoso
2. ✅ Al menos 1 prueba de caso límite
3. ✅ Al menos 1 prueba de manejo de errores
4. ✅ Validación de accesibilidad (cuando aplique)

## Comandos de Testing

### Ejecución de Pruebas

```bash
# Todas las pruebas
npm test

# Pruebas en modo watch
npm run test:watch

# Pruebas con cobertura
npm run test:coverage

# Pruebas específicas
npm test -- --testPathPattern=tramites

# Validación de métricas
npm run validate-metrics
```

### Scripts de Package.json

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false",
    "validate-metrics": "tsx scripts/validate-metrics.ts"
  }
}
```

## Integración Continua

### GitHub Actions (Recomendado)

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:ci
      - run: npm run validate-metrics
```

## Debugging de Pruebas

### Herramientas Útiles

```typescript
// Debug de componentes
import { screen, debug } from '@testing-library/react';
debug(); // Imprime el DOM actual

// Debug de queries
screen.debug(screen.getByRole('button'));

// Logs detallados
console.log('Current state:', component.state);
```

### Problemas Comunes

1. **Async/Await**: Usar `waitFor` para elementos que aparecen asincrónicamente
2. **Mocks**: Limpiar mocks entre pruebas con `jest.clearAllMocks()`
3. **DOM Cleanup**: React Testing Library limpia automáticamente
4. **Environment Variables**: Configurar en setup o mock según necesidad

## Mantenimiento

### Actualización de Pruebas

- Revisar pruebas cuando cambien APIs
- Actualizar mocks cuando cambien dependencias
- Mantener cobertura al agregar nuevas funcionalidades
- Refactorizar pruebas duplicadas

### Monitoreo de Calidad

- Ejecutar `validate-metrics` regularmente
- Revisar reportes de cobertura
- Identificar y corregir pruebas frágiles
- Mantener documentación actualizada

## Recursos Adicionales

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Supabase Testing Guide](https://supabase.com/docs/guides/getting-started/local-development)
