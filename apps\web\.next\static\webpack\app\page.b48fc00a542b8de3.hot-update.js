"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_PageLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/PageLayout */ \"(app-pages-browser)/./components/layout/PageLayout.tsx\");\n/* harmony import */ var _components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/HeroSection */ \"(app-pages-browser)/./components/landing/HeroSection.tsx\");\n/* harmony import */ var _components_landing_FeaturedServices__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/FeaturedServices */ \"(app-pages-browser)/./components/landing/FeaturedServices.tsx\");\n/* harmony import */ var _components_landing_ContactInfo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/ContactInfo */ \"(app-pages-browser)/./components/landing/ContactInfo.tsx\");\n/* harmony import */ var _components_landing_ChatWidget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/landing/ChatWidget */ \"(app-pages-browser)/./components/landing/ChatWidget.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSearchClick = ()=>{\n        // In a real implementation, this would open a search modal or navigate to search page\n        console.log('Search clicked');\n    };\n    const handleChatClick = ()=>{\n        setIsChatOpen(true);\n    };\n    const handleServiceSelect = (serviceId)=>{\n        // In a real implementation, this would navigate to the specific service\n        console.log('Service selected:', serviceId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_PageLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        onSearchClick: handleSearchClick,\n        onChatClick: handleChatClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSearchClick: handleSearchClick,\n                onChatClick: handleChatClick\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_FeaturedServices__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onServiceSelect: handleServiceSelect\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_ContactInfo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_ChatWidget__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isChatOpen,\n                onToggle: ()=>setIsChatOpen(!isChatOpen)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"1RwI3Rpum6EBbUdSXPo/Ywgstjs=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});