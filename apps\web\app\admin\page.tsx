'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ClipboardDocumentListIcon,
  UsersIcon,
  QuestionMarkCircleIcon,
  BuildingOffice2Icon,
  UserGroupIcon,
  EyeIcon,
  PencilIcon,
  PlusIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

interface DashboardStats {
  dependencias: number;
  subdependencias: number;
  tramites: number;
  opas: number;
  faqs: number;
  usuarios: number;
}

interface RecentActivity {
  id: string;
  type: 'tramite' | 'opa' | 'faq' | 'usuario';
  action: 'created' | 'updated' | 'deleted';
  title: string;
  user: string;
  timestamp: string;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    dependencias: 0,
    subdependencias: 0,
    tramites: 0,
    opas: 0,
    faqs: 0,
    usuarios: 0
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch stats from multiple endpoints
      const [dependenciasRes, tramitesRes, opasRes, faqsRes] = await Promise.all([
        fetch('/api/dependencias?limit=1'),
        fetch('/api/tramites?limit=1'),
        fetch('/api/opas?limit=1'),
        fetch('/api/faqs?limit=1')
      ]);

      const [dependenciasData, tramitesData, opasData, faqsData] = await Promise.all([
        dependenciasRes.json(),
        tramitesRes.json(),
        opasRes.json(),
        faqsRes.json()
      ]);

      setStats({
        dependencias: dependenciasData.pagination?.total || 0,
        subdependencias: 0, // TODO: Implementar conteo de subdependencias
        tramites: tramitesData.pagination?.total || 0,
        opas: opasData.pagination?.total || 0,
        faqs: faqsData.pagination?.total || 0,
        usuarios: 0 // TODO: Implementar conteo de usuarios
      });

      // TODO: Fetch recent activity from audit logs
      setRecentActivity([
        {
          id: '1',
          type: 'tramite',
          action: 'updated',
          title: 'Certificado de Residencia',
          user: 'Juan Pérez',
          timestamp: '2024-01-15T10:30:00Z'
        },
        {
          id: '2',
          type: 'opa',
          action: 'created',
          title: 'Proceso de Contratación',
          user: 'María García',
          timestamp: '2024-01-15T09:15:00Z'
        },
        {
          id: '3',
          type: 'faq',
          action: 'updated',
          title: '¿Cómo obtener certificado?',
          user: 'Carlos López',
          timestamp: '2024-01-15T08:45:00Z'
        }
      ]);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CO', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'created': return 'bg-green-100 text-green-800';
      case 'updated': return 'bg-blue-100 text-blue-800';
      case 'deleted': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'tramite': return ClipboardDocumentListIcon;
      case 'opa': return UsersIcon;
      case 'faq': return QuestionMarkCircleIcon;
      case 'usuario': return UserGroupIcon;
      default: return ClipboardDocumentListIcon;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Cargando dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Resumen general del sistema CHIA</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={fetchDashboardData}>
            Actualizar
          </Button>
          <Link href="/admin/reportes">
            <Button>
              <ChartBarIcon className="h-4 w-4 mr-2" />
              Ver Reportes
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dependencias</CardTitle>
            <BuildingOffice2Icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.dependencias}</div>
            <p className="text-xs text-muted-foreground">
              Organizaciones registradas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Trámites</CardTitle>
            <ClipboardDocumentListIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.tramites}</div>
            <p className="text-xs text-muted-foreground">
              Procedimientos disponibles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">OPAs</CardTitle>
            <UsersIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.opas}</div>
            <p className="text-xs text-muted-foreground">
              Procedimientos administrativos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">FAQs</CardTitle>
            <QuestionMarkCircleIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.faqs}</div>
            <p className="text-xs text-muted-foreground">
              Preguntas frecuentes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usuarios</CardTitle>
            <UserGroupIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.usuarios}</div>
            <p className="text-xs text-muted-foreground">
              Usuarios del sistema
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Acciones Rápidas</CardTitle>
          <CardDescription>
            Accesos directos a las funciones más utilizadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link href="/admin/tramites/nuevo">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                <PlusIcon className="h-6 w-6" />
                <span>Nuevo Trámite</span>
              </Button>
            </Link>
            
            <Link href="/admin/opas/nuevo">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                <PlusIcon className="h-6 w-6" />
                <span>Nuevo OPA</span>
              </Button>
            </Link>
            
            <Link href="/admin/faqs/nuevo">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                <PlusIcon className="h-6 w-6" />
                <span>Nueva FAQ</span>
              </Button>
            </Link>
            
            <Link href="/admin/usuarios">
              <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                <UserGroupIcon className="h-6 w-6" />
                <span>Gestionar Usuarios</span>
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Actividad Reciente</CardTitle>
          <CardDescription>
            Últimas modificaciones en el sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity) => {
              const IconComponent = getTypeIcon(activity.type);
              return (
                <div key={activity.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                  <div className="p-2 bg-white rounded-lg">
                    <IconComponent className="h-5 w-5 text-gray-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {activity.title}
                      </p>
                      <Badge className={`text-xs ${getActionColor(activity.action)}`}>
                        {activity.action === 'created' ? 'Creado' : 
                         activity.action === 'updated' ? 'Actualizado' : 'Eliminado'}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-500">
                      Por {activity.user} • {formatDate(activity.timestamp)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
