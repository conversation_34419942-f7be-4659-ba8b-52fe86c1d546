import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';

// Mock fetch for API calls
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('Hierarchical Navigation Integration Tests', () => {
  beforeAll(() => {
    // Setup test environment
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
    process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-key';
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  beforeEach(() => {
    mockFetch.mockClear();
  });

  describe('Dependencies Navigation Flow', () => {
    it('should navigate from dependencies to subdependencies to content', async () => {
      // Mock dependencies API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            {
              id: 'dep-1',
              nombre: 'Secretaría General',
              descripcion: 'Dependencia principal',
              total_tramites: 15,
              total_opas: 8,
              total_faqs: 12
            }
          ],
          pagination: { page: 1, limit: 20, total: 1, totalPages: 1 }
        })
      } as Response);

      // Mock subdependencies API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            {
              id: 'subdep-1',
              nombre: 'Oficina de Atención al Ciudadano',
              dependencia_id: 'dep-1',
              total_tramites: 10,
              total_opas: 5,
              total_faqs: 8
            }
          ]
        })
      } as Response);

      // Mock tramites filtered by dependency
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            {
              id: 'tramite-1',
              nombre: 'Certificado de Residencia',
              dependencia_id: 'dep-1',
              subdependencia_id: 'subdep-1',
              categoria: 'Certificados'
            }
          ],
          pagination: { page: 1, limit: 12, total: 10, totalPages: 1 }
        })
      } as Response);

      // Test navigation flow
      // 1. Get dependencies
      const depsResponse = await fetch('/api/dependencias');
      const depsData = await depsResponse.json();
      
      expect(depsData.success).toBe(true);
      expect(depsData.data).toHaveLength(1);
      expect(depsData.data[0].nombre).toBe('Secretaría General');

      // 2. Get subdependencies for selected dependency
      const subdepsResponse = await fetch(`/api/dependencias/dep-1/subdependencias`);
      const subdepsData = await subdepsResponse.json();
      
      expect(subdepsData.success).toBe(true);
      expect(subdepsData.data[0].dependencia_id).toBe('dep-1');

      // 3. Get tramites filtered by dependency
      const tramitesResponse = await fetch('/api/tramites?dependencia_id=dep-1');
      const tramitesData = await tramitesResponse.json();
      
      expect(tramitesData.success).toBe(true);
      expect(tramitesData.data[0].dependencia_id).toBe('dep-1');

      // Verify API calls were made in correct order
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should handle breadcrumb navigation correctly', async () => {
      // Mock API responses for breadcrumb data
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              id: 'dep-1',
              nombre: 'Secretaría General'
            }
          })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              id: 'subdep-1',
              nombre: 'Oficina de Atención',
              dependencia_id: 'dep-1'
            }
          })
        } as Response);

      // Test breadcrumb data retrieval
      const depResponse = await fetch('/api/dependencias/dep-1');
      const depData = await depResponse.json();
      
      const subdepResponse = await fetch('/api/subdependencias/subdep-1');
      const subdepData = await subdepResponse.json();

      // Verify breadcrumb structure
      expect(depData.data.nombre).toBe('Secretaría General');
      expect(subdepData.data.nombre).toBe('Oficina de Atención');
      expect(subdepData.data.dependencia_id).toBe('dep-1');
    });
  });

  describe('Content Filtering Integration', () => {
    it('should filter tramites by dependency and category', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            {
              id: 'tramite-1',
              nombre: 'Certificado de Residencia',
              categoria: 'Certificados',
              dependencia_id: 'dep-1'
            }
          ],
          pagination: { page: 1, limit: 12, total: 1, totalPages: 1 }
        })
      } as Response);

      const response = await fetch('/api/tramites?dependencia_id=dep-1&categoria=Certificados');
      const data = await response.json();

      expect(data.success).toBe(true);
      expect(data.data[0].categoria).toBe('Certificados');
      expect(data.data[0].dependencia_id).toBe('dep-1');
    });

    it('should handle search across hierarchical structure', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            {
              id: 'tramite-1',
              nombre: 'Certificado de Residencia',
              dependencia_nombre: 'Secretaría General',
              subdependencia_nombre: 'Oficina de Atención'
            }
          ],
          pagination: { page: 1, limit: 12, total: 1, totalPages: 1 }
        })
      } as Response);

      const response = await fetch('/api/tramites?search=certificado');
      const data = await response.json();

      expect(data.success).toBe(true);
      expect(data.data[0].nombre).toContain('Certificado');
    });
  });

  describe('Metrics Validation', () => {
    it('should validate dependency metrics consistency', async () => {
      // Mock dependency with metrics
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            {
              id: 'dep-1',
              nombre: 'Secretaría General',
              total_tramites: 15,
              total_opas: 8,
              total_faqs: 12
            }
          ]
        })
      } as Response);

      // Mock actual content counts
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            pagination: { total: 15 }
          })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            pagination: { total: 8 }
          })
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            pagination: { total: 12 }
          })
        } as Response);

      // Get dependency metrics
      const depResponse = await fetch('/api/dependencias');
      const depData = await depResponse.json();
      const dependency = depData.data[0];

      // Verify actual counts match metrics
      const tramitesResponse = await fetch(`/api/tramites?dependencia_id=${dependency.id}&limit=1`);
      const tramitesData = await tramitesResponse.json();

      const opasResponse = await fetch(`/api/opas?dependencia_id=${dependency.id}&limit=1`);
      const opasData = await opasResponse.json();

      const faqsResponse = await fetch(`/api/faqs?dependencia_id=${dependency.id}&limit=1`);
      const faqsData = await faqsResponse.json();

      // Validate metrics consistency
      expect(dependency.total_tramites).toBe(tramitesData.pagination.total);
      expect(dependency.total_opas).toBe(opasData.pagination.total);
      expect(dependency.total_faqs).toBe(faqsData.pagination.total);
    });

    it('should validate subdependency aggregation', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            {
              id: 'subdep-1',
              nombre: 'Oficina 1',
              dependencia_id: 'dep-1',
              total_tramites: 8
            },
            {
              id: 'subdep-2',
              nombre: 'Oficina 2',
              dependencia_id: 'dep-1',
              total_tramites: 7
            }
          ]
        })
      } as Response);

      const response = await fetch('/api/dependencias/dep-1/subdependencias');
      const data = await response.json();

      const totalTramites = data.data.reduce((sum: number, sub: any) => sum + sub.total_tramites, 0);
      expect(totalTramites).toBe(15); // Should match parent dependency total
    });
  });

  describe('Error Handling in Navigation', () => {
    it('should handle missing dependency gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({
          success: false,
          error: 'Dependencia no encontrada'
        })
      } as Response);

      const response = await fetch('/api/dependencias/non-existent');
      const data = await response.json();

      expect(data.success).toBe(false);
      expect(data.error).toBe('Dependencia no encontrada');
    });

    it('should handle empty subdependencies', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: []
        })
      } as Response);

      const response = await fetch('/api/dependencias/dep-1/subdependencias');
      const data = await response.json();

      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(0);
    });

    it('should handle network errors in navigation', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      try {
        await fetch('/api/dependencias');
        expect(true).toBe(false); // Should not reach here
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Network error');
      }
    });
  });

  describe('Performance Validation', () => {
    it('should load dependencies within acceptable time', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: Array.from({ length: 50 }, (_, i) => ({
            id: `dep-${i}`,
            nombre: `Dependencia ${i}`,
            total_tramites: Math.floor(Math.random() * 20)
          }))
        })
      } as Response);

      const startTime = Date.now();
      const response = await fetch('/api/dependencias');
      await response.json();
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle pagination efficiently', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: Array.from({ length: 20 }, (_, i) => ({
            id: `tramite-${i}`,
            nombre: `Trámite ${i}`
          })),
          pagination: { page: 1, limit: 20, total: 100, totalPages: 5 }
        })
      } as Response);

      const response = await fetch('/api/tramites?page=1&limit=20');
      const data = await response.json();

      expect(data.data).toHaveLength(20);
      expect(data.pagination.totalPages).toBe(5);
    });
  });
});
