"use strict";exports.id=5194,exports.ids=[5194],exports.modules={91823:(e,r,t)=>{t.d(r,{Fc:()=>m,TN:()=>x,lr:()=>A,$n:()=>b,Zp:()=>d,Wu:()=>p,BT:()=>u,aR:()=>c,ZB:()=>f,Sc:()=>v,pd:()=>g,WR:()=>z,tw:()=>S,l6:()=>h,gC:()=>y,eb:()=>w,bq:()=>N,yv:()=>j,e6:()=>C,do:()=>R});var a=t(13486),o=t(60159),i=t.n(o),n=t(4627),s=t(55855);function l(...e){return(0,s.QP)((0,n.$)(e))}let d=o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:l("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));d.displayName="Card";let c=o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:l("flex flex-col space-y-1.5 p-6",e),...r}));c.displayName="CardHeader";let f=o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("h3",{ref:t,className:l("text-2xl font-semibold leading-none tracking-tight",e),...r}));f.displayName="CardTitle";let u=o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("p",{ref:t,className:l("text-sm text-muted-foreground",e),...r}));u.displayName="CardDescription";let p=o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:l("p-6 pt-0",e),...r}));p.displayName="CardContent",o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:l("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let g=o.forwardRef(({className:e,type:r,error:t,...o},i)=>(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("input",{type:r,className:l("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t&&"border-red-500 focus-visible:ring-red-500",e),ref:i,...o}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:t})]}));g.displayName="Input",o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("label",{ref:t,className:l("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...r})).displayName="Label";let b=o.forwardRef(({className:e,variant:r="default",size:t="default",...o},i)=>(0,a.jsx)("button",{className:l("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===r,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===r,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===r,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===r,"hover:bg-accent hover:text-accent-foreground":"ghost"===r,"text-primary underline-offset-4 hover:underline":"link"===r},{"h-10 px-4 py-2":"default"===t,"h-9 rounded-md px-3":"sm"===t,"h-11 rounded-md px-8":"lg"===t,"h-10 w-10":"icon"===t},e),ref:i,...o}));b.displayName="Button";let m=o.forwardRef(({className:e,variant:r="default",...t},o)=>(0,a.jsx)("div",{ref:o,role:"alert",className:l("relative w-full rounded-lg border p-4",{"bg-background text-foreground":"default"===r,"border-destructive/50 text-destructive dark:border-destructive":"destructive"===r},e),...t}));m.displayName="Alert";let x=o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:l("text-sm [&_p]:leading-relaxed",e),...r}));x.displayName="AlertDescription";let v=o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("input",{type:"checkbox",className:l("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...r}));v.displayName="Checkbox";let h=o.forwardRef(({className:e,children:r,onValueChange:t,...o},i)=>(0,a.jsx)("select",{className:l("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,onChange:e=>{t?.(e.target.value),o.onChange?.(e)},...o,children:r}));h.displayName="Select";let y=o.forwardRef(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:l("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md",e),...r}));y.displayName="SelectContent";let w=o.forwardRef(({className:e,children:r,...t},o)=>(0,a.jsx)("option",{ref:o,className:l("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none",e),...t,children:r}));w.displayName="SelectItem";let N=h,j=({placeholder:e})=>(0,a.jsx)("span",{className:"text-muted-foreground",children:e});var k=t(54528);let _={initial:{"--x":"100%",scale:.8},animate:{"--x":"-100%",scale:1},whileTap:{scale:.95},transition:{repeat:1/0,repeatType:"loop",repeatDelay:1,type:"spring",stiffness:20,damping:15,mass:2,scale:{type:"spring",stiffness:200,damping:5,mass:.5}}},R=i().forwardRef(({children:e,className:r,...t},o)=>(0,a.jsxs)(k.P.button,{ref:o,className:l("relative cursor-pointer rounded-lg px-6 py-2 font-medium backdrop-blur-xl border transition-shadow duration-300 ease-in-out hover:shadow dark:bg-[radial-gradient(circle_at_50%_0%,var(--primary)/10%_0%,transparent_60%)] dark:hover:shadow-[0_0_20px_var(--primary)/10%]",r),..._,...t,children:[(0,a.jsx)("span",{className:"relative block size-full text-sm uppercase tracking-wide text-[rgb(0,0,0,65%)] dark:font-light dark:text-[rgb(255,255,255,90%)]",style:{maskImage:"linear-gradient(-75deg,var(--primary) calc(var(--x) + 20%),transparent calc(var(--x) + 30%),var(--primary) calc(var(--x) + 100%))"},children:e}),(0,a.jsx)("span",{style:{mask:"linear-gradient(rgb(0,0,0), rgb(0,0,0)) content-box exclude,linear-gradient(rgb(0,0,0), rgb(0,0,0))",WebkitMask:"linear-gradient(rgb(0,0,0), rgb(0,0,0)) content-box exclude,linear-gradient(rgb(0,0,0), rgb(0,0,0))",backgroundImage:"linear-gradient(-75deg,var(--primary)/10% calc(var(--x)+20%),var(--primary)/50% calc(var(--x)+25%),var(--primary)/10% calc(var(--x)+100%))"},className:"absolute inset-0 z-10 block rounded-[inherit] p-px"})]}));R.displayName="ShinyButton";let C=i().forwardRef(({shimmerColor:e="#ffffff",shimmerSize:r="0.05em",shimmerDuration:t="3s",borderRadius:o="100px",background:i="rgba(0, 0, 0, 1)",className:n,children:s,...d},c)=>(0,a.jsxs)("button",{style:{"--spread":"90deg","--shimmer-color":e,"--radius":o,"--speed":t,"--cut":r,"--bg":i},className:l("group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 px-6 py-3 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black","transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px",n),ref:c,...d,children:[(0,a.jsx)("div",{className:l("-z-30 blur-[2px]","absolute inset-0 overflow-visible [container-type:size]"),children:(0,a.jsx)("div",{className:"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]",children:(0,a.jsx)("div",{className:"absolute -inset-full w-auto rotate-0 animate-spin-around [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]"})})}),s,(0,a.jsx)("div",{className:l("insert-0 absolute size-full","rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]","transform-gpu transition-all duration-300 ease-in-out","group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]","group-active:shadow-[inset_0_-10px_10px_#ffffff3f]")}),(0,a.jsx)("div",{className:l("absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]")})]}));C.displayName="ShimmerButton";let z=i().forwardRef(({className:e,children:r,pulseColor:t="#808080",duration:o="1.5s",...i},n)=>(0,a.jsxs)("button",{ref:n,className:l("relative flex cursor-pointer items-center justify-center rounded-lg bg-primary px-4 py-2 text-center text-primary-foreground",e),style:{"--pulse-color":t,"--duration":o},...i,children:[(0,a.jsx)("div",{className:"relative z-10",children:r}),(0,a.jsx)("div",{className:"absolute left-1/2 top-1/2 size-full -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-lg bg-inherit"})]}));z.displayName="PulsatingButton";var B=t(90691);let $=(0,t(76353).F)(l("relative cursor-pointer group transition-all animate-rainbow","inline-flex items-center justify-center gap-2 shrink-0","rounded-sm outline-none focus-visible:ring-[3px] aria-invalid:border-destructive","text-sm font-medium whitespace-nowrap","disabled:pointer-events-none disabled:opacity-50","[&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0"),{variants:{variant:{default:"border-0 bg-[linear-gradient(#121213,#121213),linear-gradient(#121213_50%,rgba(18,18,19,0.6)_80%,rgba(18,18,19,0)),linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))] bg-[length:200%] text-primary-foreground [background-clip:padding-box,border-box,border-box] [background-origin:border-box] [border:calc(0.125rem)_solid_transparent] before:absolute before:bottom-[-20%] before:left-1/2 before:z-0 before:h-1/5 before:w-3/5 before:-translate-x-1/2 before:animate-rainbow before:bg-[linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))] before:[filter:blur(0.75rem)] dark:bg-[linear-gradient(#fff,#fff),linear-gradient(#fff_50%,rgba(255,255,255,0.6)_80%,rgba(0,0,0,0)),linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))]",outline:"border border-input border-b-transparent bg-[linear-gradient(#ffffff,#ffffff),linear-gradient(#ffffff_50%,rgba(18,18,19,0.6)_80%,rgba(18,18,19,0)),linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))] bg-[length:200%] text-accent-foreground [background-clip:padding-box,border-box,border-box] [background-origin:border-box] before:absolute before:bottom-[-20%] before:left-1/2 before:z-0 before:h-1/5 before:w-3/5 before:-translate-x-1/2 before:animate-rainbow before:bg-[linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))] before:[filter:blur(0.75rem)] dark:bg-[linear-gradient(#0a0a0a,#0a0a0a),linear-gradient(#0a0a0a_50%,rgba(255,255,255,0.6)_80%,rgba(0,0,0,0)),linear-gradient(90deg,var(--color-1),var(--color-5),var(--color-3),var(--color-4),var(--color-2))]"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-xl px-3 text-xs",lg:"h-11 rounded-xl px-8",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),S=i().forwardRef(({className:e,variant:r,size:t,asChild:o=!1,...i},n)=>{let s=o?B.DX:"button";return(0,a.jsx)(s,{"data-slot":"button",className:l($({variant:r,size:t,className:e})),ref:n,...i})});S.displayName="RainbowButton";var T=t(31461);i().forwardRef(({children:e,className:r,...t},o)=>(0,a.jsxs)("button",{ref:o,className:l("group relative w-auto cursor-pointer overflow-hidden rounded-full border bg-background p-2 px-6 text-center font-semibold",r),...t,children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full bg-primary transition-all duration-300 group-hover:scale-[100.8]"}),(0,a.jsx)("span",{className:"inline-block transition-all duration-300 group-hover:translate-x-12 group-hover:opacity-0",children:e})]}),(0,a.jsxs)("div",{className:"absolute top-0 z-10 flex h-full w-full translate-x-12 items-center justify-center gap-2 text-primary-foreground opacity-0 transition-all duration-300 group-hover:-translate-x-5 group-hover:opacity-100",children:[(0,a.jsx)("span",{children:e}),(0,a.jsx)(T.A,{})]})]})).displayName="InteractiveHoverButton";var P=t(4653);i().forwardRef(({subscribeStatus:e=!1,onClick:r,className:t,children:n,...s},d)=>{let[c,f]=(0,o.useState)(e);if(2!==i().Children.count(n)||!i().Children.toArray(n).every(e=>i().isValidElement(e)&&"span"===e.type))throw Error("AnimatedSubscribeButton expects two children, both of which must be <span> elements.");let u=i().Children.toArray(n),p=u[0],g=u[1];return(0,a.jsx)(P.N,{mode:"wait",children:c?(0,a.jsx)(k.P.button,{ref:d,className:l("relative flex h-10 w-fit items-center justify-center overflow-hidden rounded-lg bg-primary px-6 text-primary-foreground",t),onClick:e=>{f(!1),r?.(e)},initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},...s,children:(0,a.jsxs)(k.P.span,{className:"relative flex h-full w-full items-center justify-center font-semibold",initial:{y:-50},animate:{y:0},children:[g," "]},"action")}):(0,a.jsx)(k.P.button,{ref:d,className:l("relative flex h-10 w-fit cursor-pointer items-center justify-center rounded-lg border-none bg-primary px-6 text-primary-foreground",t),onClick:e=>{f(!0),r?.(e)},initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},...s,children:(0,a.jsxs)(k.P.span,{className:"relative flex items-center justify-center font-semibold",initial:{x:0},exit:{x:50,transition:{duration:.1}},children:[p," "]},"reaction")})})}).displayName="AnimatedSubscribeButton",i().forwardRef(({className:e,children:r,rippleColor:t="#ffffff",duration:i="600ms",onClick:n,...s},d)=>{let[c,f]=(0,o.useState)([]),u=e=>{let r=e.currentTarget.getBoundingClientRect(),t=Math.max(r.width,r.height),a={x:e.clientX-r.left-t/2,y:e.clientY-r.top-t/2,size:t,key:Date.now()};f(e=>[...e,a])};return(0,o.useEffect)(()=>{if(c.length>0){let e=c[c.length-1],r=setTimeout(()=>{f(r=>r.filter(r=>r.key!==e.key))},parseInt(i));return()=>clearTimeout(r)}},[c,i]),(0,a.jsxs)("button",{className:l("relative flex cursor-pointer items-center justify-center overflow-hidden rounded-lg border-2 bg-background px-4 py-2 text-center text-primary",e),onClick:e=>{u(e),n?.(e)},ref:d,...s,children:[(0,a.jsx)("div",{className:"relative z-10",children:r}),(0,a.jsx)("span",{className:"pointer-events-none absolute inset-0",children:c.map(e=>(0,a.jsx)("span",{className:"absolute animate-rippling rounded-full bg-background opacity-30",style:{width:`${e.size}px`,height:`${e.size}px`,top:`${e.y}px`,left:`${e.x}px`,backgroundColor:t,transform:"scale(0)"}},e.key))})]})}).displayName="RippleButton";let A=({className:e,containerRef:r,fromRef:t,toRef:i,curvature:n=0,reverse:s=!1,duration:d=3*Math.random()+4,delay:c=0,pathColor:f="gray",pathWidth:u=2,pathOpacity:p=.2,gradientStartColor:g="#ffaa40",gradientStopColor:b="#9c40ff",startXOffset:m=0,startYOffset:x=0,endXOffset:v=0,endYOffset:h=0})=>{let y=(0,o.useId)(),[w,N]=(0,o.useState)(""),[j,_]=(0,o.useState)({width:0,height:0}),R=s?{x1:["90%","-10%"],x2:["100%","0%"],y1:["0%","0%"],y2:["0%","0%"]}:{x1:["10%","110%"],x2:["0%","100%"],y1:["0%","0%"],y2:["0%","0%"]};return(0,o.useEffect)(()=>{let e=()=>{if(r.current&&t.current&&i.current){let e=r.current.getBoundingClientRect(),a=t.current.getBoundingClientRect(),o=i.current.getBoundingClientRect();_({width:e.width,height:e.height});let s=a.left-e.left+a.width/2+m,l=a.top-e.top+a.height/2+x,d=o.left-e.left+o.width/2+v,c=o.top-e.top+o.height/2+h;N(`M ${s},${l} Q ${(s+d)/2},${l-n} ${d},${c}`)}},a=new ResizeObserver(r=>{for(let t of r)e()});return r.current&&a.observe(r.current),e(),()=>{a.disconnect()}},[r,t,i,n,m,x,v,h]),(0,a.jsxs)("svg",{fill:"none",width:j.width,height:j.height,xmlns:"http://www.w3.org/2000/svg",className:l("pointer-events-none absolute left-0 top-0 transform-gpu stroke-2",e),viewBox:`0 0 ${j.width} ${j.height}`,children:[(0,a.jsx)("path",{d:w,stroke:f,strokeWidth:u,strokeOpacity:p,strokeLinecap:"round"}),(0,a.jsx)("path",{d:w,strokeWidth:u,stroke:`url(#${y})`,strokeOpacity:"1",strokeLinecap:"round"}),(0,a.jsx)("defs",{children:(0,a.jsxs)(k.P.linearGradient,{className:"transform-gpu",id:y,gradientUnits:"userSpaceOnUse",initial:{x1:"0%",x2:"0%",y1:"0%",y2:"0%"},animate:{x1:R.x1,x2:R.x2,y1:R.y1,y2:R.y2},transition:{delay:c,duration:d,ease:[.16,1,.3,1],repeat:1/0,repeatDelay:0},children:[(0,a.jsx)("stop",{stopColor:g,stopOpacity:"0"}),(0,a.jsx)("stop",{stopColor:g}),(0,a.jsx)("stop",{offset:"32.5%",stopColor:b}),(0,a.jsx)("stop",{offset:"100%",stopColor:b,stopOpacity:"0"})]})})]})}},95194:(e,r,t)=>{t.d(r,{BT:()=>a.BT,Wu:()=>a.Wu,ZB:()=>a.ZB,Zp:()=>a.Zp,aR:()=>a.aR});var a=t(91823)}};