'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Plus, 
  PencilIcon,
  TrashIcon,
  EyeIcon,
  Filter,
  Building2,
  Clock,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

interface Tramite {
  id: string;
  nombre: string;
  descripcion: string;
  categoria: string;
  dependencia_nombre: string;
  subdependencia_nombre: string;
  tiempo_estimado: string;
  costo: string;
  modalidad: string;
  estado: string;
  created_at: string;
  updated_at: string;
}

interface ApiResponse {
  success: boolean;
  data: Tramite[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}

export default function AdminTramitesPage() {
  const [tramites, setTramites] = useState<Tramite[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategoria, setSelectedCategoria] = useState<string>('');
  const [selectedEstado, setSelectedEstado] = useState<string>('');
  const [categorias, setCategorias] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  useEffect(() => {
    fetchTramites();
    fetchCategorias();
  }, [currentPage, searchTerm, selectedCategoria, selectedEstado]);

  const fetchTramites = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      });

      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategoria) params.append('categoria', selectedCategoria);
      if (selectedEstado) params.append('estado', selectedEstado);

      const response = await fetch(`/api/tramites?${params}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar trámites');
      }

      const data: ApiResponse = await response.json();
      
      if (data.success) {
        setTramites(data.data);
        setPagination(data.pagination);
      } else {
        throw new Error(data.error || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategorias = async () => {
    try {
      const response = await fetch('/api/tramites/categorias');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCategorias(data.data);
        }
      }
    } catch (err) {
      console.error('Error al cargar categorías:', err);
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleCategoriaChange = (value: string) => {
    setSelectedCategoria(value === 'all' ? '' : value);
    setCurrentPage(1);
  };

  const handleEstadoChange = (value: string) => {
    setSelectedEstado(value === 'all' ? '' : value);
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategoria('');
    setSelectedEstado('');
    setCurrentPage(1);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('¿Estás seguro de que deseas eliminar este trámite?')) {
      return;
    }

    try {
      const response = await fetch(`/api/tramites/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchTramites(); // Refresh the list
      } else {
        alert('Error al eliminar el trámite');
      }
    } catch (error) {
      console.error('Error deleting tramite:', error);
      alert('Error al eliminar el trámite');
    }
  };

  if (loading && tramites.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Cargando trámites...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestión de Trámites</h1>
          <p className="text-gray-600">Administra los trámites municipales</p>
        </div>
        <Link href="/admin/tramites/nuevo">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Trámite
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Buscar trámites..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedCategoria || 'all'} onValueChange={handleCategoriaChange}>
              <SelectTrigger>
                <SelectValue placeholder="Todas las categorías" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas las categorías</SelectItem>
                {categorias.map((categoria) => (
                  <SelectItem key={categoria} value={categoria}>
                    {categoria}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedEstado || 'all'} onValueChange={handleEstadoChange}>
              <SelectTrigger>
                <SelectValue placeholder="Todos los estados" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="activo">Activo</SelectItem>
                <SelectItem value="inactivo">Inactivo</SelectItem>
                <SelectItem value="borrador">Borrador</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={clearFilters}>
              <Filter className="h-4 w-4 mr-2" />
              Limpiar Filtros
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Mostrando {tramites.length} de {pagination.total} trámites
        </p>
        <div className="text-sm text-gray-500">
          Página {pagination.page} de {pagination.totalPages}
        </div>
      </div>

      {/* Tramites Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Nombre</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Categoría</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Dependencia</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Estado</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Tiempo</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Costo</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Acciones</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {tramites.map((tramite) => (
                  <tr key={tramite.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <p className="font-medium text-gray-900 line-clamp-1">
                          {tramite.nombre}
                        </p>
                        {tramite.descripcion && (
                          <p className="text-sm text-gray-500 line-clamp-1">
                            {tramite.descripcion}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge variant="secondary">{tramite.categoria}</Badge>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Building2 className="h-4 w-4 mr-1" />
                        <span className="truncate">
                          {tramite.dependencia_nombre}
                          {tramite.subdependencia_nombre && ` - ${tramite.subdependencia_nombre}`}
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge 
                        variant={tramite.estado === 'activo' ? 'default' : 'secondary'}
                      >
                        {tramite.estado}
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      {tramite.tiempo_estimado && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>{tramite.tiempo_estimado}</span>
                        </div>
                      )}
                    </td>
                    <td className="py-3 px-4">
                      {tramite.costo && (
                        <div className="flex items-center text-sm text-gray-600">
                          <DollarSign className="h-4 w-4 mr-1" />
                          <span>{tramite.costo}</span>
                        </div>
                      )}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center justify-end space-x-2">
                        <Link href={`/tramites/${tramite.id}`}>
                          <Button variant="ghost" size="sm">
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/admin/tramites/${tramite.id}/editar`}>
                          <Button variant="ghost" size="sm">
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDelete(tramite.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1 || loading}
          >
            Anterior
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                  disabled={loading}
                >
                  {page}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            onClick={() => setCurrentPage(Math.min(pagination.totalPages, currentPage + 1))}
            disabled={currentPage === pagination.totalPages || loading}
          >
            Siguiente
          </Button>
        </div>
      )}

      {tramites.length === 0 && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📄</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No se encontraron trámites
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedCategoria || selectedEstado 
                ? 'Intenta ajustar los filtros de búsqueda'
                : 'Comienza creando tu primer trámite'
              }
            </p>
            {!searchTerm && !selectedCategoria && !selectedEstado && (
              <Link href="/admin/tramites/nuevo">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Crear Primer Trámite
                </Button>
              </Link>
            )}
            {(searchTerm || selectedCategoria || selectedEstado) && (
              <Button onClick={clearFilters}>
                Limpiar Filtros
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
