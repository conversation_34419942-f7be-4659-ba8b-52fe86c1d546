(()=>{var e={};e.id=7443,e.ids=[7443],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24768:(e,r,t)=>{"use strict";t.d(r,{y8:()=>i});var s=t(2492),a=t(62518);let n="https://hndowofzjzjoljnapokv.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(!n||!o)throw Error("Missing Supabase environment variables");let i=async()=>{let{cookies:e}=await t.e(5208).then(t.bind(t,65208)),r=e();return(0,a.createServerComponentClient)({cookies:()=>r})};(0,s.UU)(n,o)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},37652:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>d,GET:()=>u,PUT:()=>c});var a=t(48106),n=t(48819),o=t(12050),i=t(4235),p=t(24768);async function u(e,{params:r}){try{let{id:e}=r,t=(0,p.y8)(),{data:s,error:a}=await t.from("opas_view").select("*").eq("id",e).single();if(a||!s)return i.NextResponse.json({error:"OPA no encontrada"},{status:404});return i.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Unexpected error in OPA detail API:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function c(e,{params:r}){try{let{id:t}=r,s=await e.json(),a=(0,p.y8)(),{data:{user:n},error:o}=await a.auth.getUser();if(o||!n)return i.NextResponse.json({error:"No autorizado"},{status:401});let{data:u,error:c}=await a.schema("ingestion").from("opas").select("subdependencia_id, subdependencias(dependencia_id)").eq("id",t).single();if(c||!u)return i.NextResponse.json({error:"OPA no encontrada"},{status:404});let{data:d}=await a.rpc("check_user_permission",{p_user_id:n.id,p_action:"UPDATE",p_table_name:"opas",p_dependencia_id:u.subdependencias?.dependencia_id});if(!d)return i.NextResponse.json({error:"Sin permisos para modificar esta OPA"},{status:403});let{data:l,error:x}=await a.schema("ingestion").from("opas").update({...s,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(x)return console.error("Error updating OPA:",x),i.NextResponse.json({error:"Error al actualizar OPA"},{status:500});return i.NextResponse.json({success:!0,data:l})}catch(e){return console.error("Error in OPA PUT:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function d(e,{params:r}){try{let{id:e}=r,t=(0,p.y8)(),{data:{user:s},error:a}=await t.auth.getUser();if(a||!s)return i.NextResponse.json({error:"No autorizado"},{status:401});let{data:n,error:o}=await t.schema("ingestion").from("opas").select("subdependencia_id, subdependencias(dependencia_id)").eq("id",e).single();if(o||!n)return i.NextResponse.json({error:"OPA no encontrada"},{status:404});let{data:u}=await t.rpc("check_user_permission",{p_user_id:s.id,p_action:"DELETE",p_table_name:"opas",p_dependencia_id:n.subdependencias?.dependencia_id});if(!u)return i.NextResponse.json({error:"Sin permisos para eliminar esta OPA"},{status:403});let{error:c}=await t.schema("ingestion").from("opas").update({activo:!1,updated_at:new Date().toISOString()}).eq("id",e);if(c)return console.error("Error deactivating OPA:",c),i.NextResponse.json({error:"Error al eliminar OPA"},{status:500});return i.NextResponse.json({success:!0,message:"OPA eliminada correctamente"})}catch(e){return console.error("Error in OPA DELETE:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/opas/[id]/route",pathname:"/api/opas/[id]",filename:"route",bundlePath:"app/api/opas/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\opas\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:h}=l;function g(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744,2518],()=>t(37652));module.exports=s})();