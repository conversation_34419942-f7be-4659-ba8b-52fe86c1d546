-- =====================================================
-- CONSULTAS SQL PARA FRONTEND - SISTEMA CHIA
-- Fecha: 2025-01-08
-- Estado: Producción Ready
-- =====================================================

-- =====================================================
-- 1. BÚSQUEDA GENERAL UNIFICADA (RECOMENDADA)
-- =====================================================
-- Parámetros: $1 = término de búsqueda, $2 = límite de resultados
WITH busqueda_general AS (
  -- Búsqueda en Trámites
  SELECT 
    'tramite' as tipo,
    t.id,
    t.nombre as titulo,
    t.descripcion,
    d.nombre as dependencia,
    s.nombre as subdependencia,
    t.costo,
    t.tiempo_estimado,
    t.modalidad,
    t.url_tramite as url,
    ts_rank(t.search_vector, to_tsquery('spanish', $1)) as relevancia
  FROM ingestion.tramites t
  JOIN ingestion.dependencias d ON t.dependencia_id = d.id
  LEFT JOIN ingestion.subdependencias s ON t.subdependencia_id = s.id
  WHERE t.search_vector @@ to_tsquery('spanish', $1)
  
  UNION ALL
  
  -- Búsqueda en OPAs
  SELECT 
    'opa' as tipo,
    o.id,
    o.nombre as titulo,
    o.descripcion,
    d.nombre as dependencia,
    s.nombre as subdependencia,
    NULL as costo,
    NULL as tiempo_estimado,
    NULL as modalidad,
    NULL as url,
    ts_rank(o.search_vector, to_tsquery('spanish', $1)) as relevancia
  FROM ingestion.opas o
  JOIN ingestion.dependencias d ON o.dependencia_id = d.id
  LEFT JOIN ingestion.subdependencias s ON o.subdependencia_id = s.id
  WHERE o.search_vector @@ to_tsquery('spanish', $1)
  
  UNION ALL
  
  -- Búsqueda en FAQs
  SELECT 
    'faq' as tipo,
    f.id,
    f.pregunta as titulo,
    f.respuesta as descripcion,
    d.nombre as dependencia,
    NULL as subdependencia,
    NULL as costo,
    NULL as tiempo_estimado,
    NULL as modalidad,
    NULL as url,
    ts_rank(f.search_vector, to_tsquery('spanish', $1)) as relevancia
  FROM ingestion.faqs f
  JOIN ingestion.dependencias d ON f.dependencia_id = d.id
  WHERE f.search_vector @@ to_tsquery('spanish', $1)
)
SELECT 
  tipo,
  id,
  titulo,
  descripcion,
  dependencia,
  COALESCE(subdependencia, 'N/A') as subdependencia,
  COALESCE(costo, 'N/A') as costo,
  COALESCE(tiempo_estimado, 'N/A') as tiempo_estimado,
  COALESCE(modalidad, 'N/A') as modalidad,
  COALESCE(url, 'N/A') as url,
  ROUND(relevancia::numeric, 3) as puntuacion_relevancia
FROM busqueda_general
ORDER BY relevancia DESC, tipo, titulo
LIMIT $2;

-- =====================================================
-- 2. BÚSQUEDA CON HIGHLIGHTING
-- =====================================================
-- Parámetros: $1 = término de búsqueda, $2 = límite de resultados
SELECT 
  'TRAMITE' as tipo_contenido,
  t.id,
  COALESCE(t.codigo_suit, 'N/A') as codigo,
  ts_headline('spanish', t.nombre, to_tsquery('spanish', $1), 
    'StartSel=<mark>, StopSel=</mark>') as titulo_destacado,
  ts_headline('spanish', COALESCE(t.descripcion, ''), to_tsquery('spanish', $1), 
    'StartSel=<mark>, StopSel=</mark>') as descripcion_destacada,
  d.nombre as dependencia,
  COALESCE(s.nombre, 'N/A') as subdependencia,
  t.costo,
  t.tiempo_estimado,
  ts_rank(t.search_vector, to_tsquery('spanish', $1)) as relevancia
FROM ingestion.tramites t
JOIN ingestion.dependencias d ON t.dependencia_id = d.id
LEFT JOIN ingestion.subdependencias s ON t.subdependencia_id = s.id
WHERE t.search_vector @@ to_tsquery('spanish', $1)

UNION ALL

SELECT 
  'OPA' as tipo_contenido,
  o.id,
  o.codigo_opa as codigo,
  ts_headline('spanish', o.nombre, to_tsquery('spanish', $1), 
    'StartSel=<mark>, StopSel=</mark>') as titulo_destacado,
  ts_headline('spanish', COALESCE(o.descripcion, ''), to_tsquery('spanish', $1), 
    'StartSel=<mark>, StopSel=</mark>') as descripcion_destacada,
  d.nombre as dependencia,
  COALESCE(s.nombre, 'N/A') as subdependencia,
  NULL as costo,
  NULL as tiempo_estimado,
  ts_rank(o.search_vector, to_tsquery('spanish', $1)) as relevancia
FROM ingestion.opas o
JOIN ingestion.dependencias d ON o.dependencia_id = d.id
LEFT JOIN ingestion.subdependencias s ON o.subdependencia_id = s.id
WHERE o.search_vector @@ to_tsquery('spanish', $1)

ORDER BY relevancia DESC, tipo_contenido, titulo_destacado
LIMIT $2;

-- =====================================================
-- 3. BÚSQUEDA POR DEPENDENCIA
-- =====================================================
-- Parámetros: $1 = ID de dependencia, $2 = término de búsqueda (opcional), $3 = límite
SELECT 
  'tramite' as tipo,
  t.id,
  t.nombre as titulo,
  t.descripcion,
  d.nombre as dependencia,
  s.nombre as subdependencia,
  t.costo,
  t.tiempo_estimado
FROM ingestion.tramites t
JOIN ingestion.dependencias d ON t.dependencia_id = d.id
LEFT JOIN ingestion.subdependencias s ON t.subdependencia_id = s.id
WHERE t.dependencia_id = $1
  AND ($2 IS NULL OR t.search_vector @@ to_tsquery('spanish', $2))

UNION ALL

SELECT 
  'opa' as tipo,
  o.id,
  o.nombre as titulo,
  o.descripcion,
  d.nombre as dependencia,
  s.nombre as subdependencia,
  NULL as costo,
  NULL as tiempo_estimado
FROM ingestion.opas o
JOIN ingestion.dependencias d ON o.dependencia_id = d.id
LEFT JOIN ingestion.subdependencias s ON o.subdependencia_id = s.id
WHERE o.dependencia_id = $1
  AND ($2 IS NULL OR o.search_vector @@ to_tsquery('spanish', $2))

ORDER BY tipo, titulo
LIMIT $3;

-- =====================================================
-- 4. OBTENER TODAS LAS DEPENDENCIAS (PARA FILTROS)
-- =====================================================
SELECT 
  d.id,
  d.codigo,
  d.nombre,
  d.descripcion,
  (SELECT COUNT(*) FROM ingestion.tramites WHERE dependencia_id = d.id) as total_tramites,
  (SELECT COUNT(*) FROM ingestion.opas WHERE dependencia_id = d.id) as total_opas,
  (SELECT COUNT(*) FROM ingestion.faqs WHERE dependencia_id = d.id) as total_faqs
FROM ingestion.dependencias d
ORDER BY d.nombre;

-- =====================================================
-- 5. OBTENER SUBDEPENDENCIAS POR DEPENDENCIA
-- =====================================================
-- Parámetro: $1 = ID de dependencia
SELECT 
  s.id,
  s.codigo,
  s.nombre,
  s.descripcion,
  (SELECT COUNT(*) FROM ingestion.tramites WHERE subdependencia_id = s.id) as total_tramites,
  (SELECT COUNT(*) FROM ingestion.opas WHERE subdependencia_id = s.id) as total_opas
FROM ingestion.subdependencias s
WHERE s.dependencia_id = $1
ORDER BY s.nombre;

-- =====================================================
-- 6. DETALLE COMPLETO DE TRÁMITE
-- =====================================================
-- Parámetro: $1 = ID del trámite
SELECT 
  t.id,
  t.nombre,
  t.descripcion,
  t.requisitos,
  t.documentos_requeridos,
  t.costo,
  t.tiempo_estimado,
  t.modalidad,
  t.url_tramite,
  t.palabras_clave,
  t.categoria,
  t.complejidad,
  t.formulario_requerido,
  t.tiempo_respuesta,
  t.tiene_pago,
  t.descripcion_pago,
  t.url_suit,
  t.url_gov_co,
  t.codigo_suit,
  d.nombre as dependencia,
  d.descripcion as dependencia_descripcion,
  s.nombre as subdependencia,
  s.descripcion as subdependencia_descripcion
FROM ingestion.tramites t
JOIN ingestion.dependencias d ON t.dependencia_id = d.id
LEFT JOIN ingestion.subdependencias s ON t.subdependencia_id = s.id
WHERE t.id = $1;

-- =====================================================
-- 7. DETALLE COMPLETO DE OPA
-- =====================================================
-- Parámetro: $1 = ID del OPA
SELECT 
  o.id,
  o.codigo_opa,
  o.nombre,
  o.descripcion,
  d.nombre as dependencia,
  d.descripcion as dependencia_descripcion,
  s.nombre as subdependencia,
  s.descripcion as subdependencia_descripcion,
  o.metadata,
  o.created_at,
  o.updated_at
FROM ingestion.opas o
JOIN ingestion.dependencias d ON o.dependencia_id = d.id
LEFT JOIN ingestion.subdependencias s ON o.subdependencia_id = s.id
WHERE o.id = $1;

-- =====================================================
-- 8. FAQS POR TEMA
-- =====================================================
-- Parámetro: $1 = tema (opcional)
SELECT 
  f.id,
  f.pregunta,
  f.respuesta,
  f.tema,
  d.nombre as dependencia,
  f.orden_tema,
  f.metadata
FROM ingestion.faqs f
JOIN ingestion.dependencias d ON f.dependencia_id = d.id
WHERE ($1 IS NULL OR f.tema = $1)
ORDER BY f.tema, f.orden_tema;

-- =====================================================
-- 9. ESTADÍSTICAS GENERALES (PARA DASHBOARD)
-- =====================================================
SELECT 
  'estadisticas_generales' as tipo,
  (SELECT COUNT(*) FROM ingestion.tramites) as total_tramites,
  (SELECT COUNT(*) FROM ingestion.opas) as total_opas,
  (SELECT COUNT(*) FROM ingestion.faqs) as total_faqs,
  (SELECT COUNT(*) FROM ingestion.dependencias) as total_dependencias,
  (SELECT COUNT(*) FROM ingestion.subdependencias) as total_subdependencias,
  (SELECT COUNT(*) FROM ingestion.tramites) + 
  (SELECT COUNT(*) FROM ingestion.opas) + 
  (SELECT COUNT(*) FROM ingestion.faqs) as total_contenido_busqueda;

-- =====================================================
-- 10. BÚSQUEDA SUGERENCIAS (AUTOCOMPLETADO)
-- =====================================================
-- Parámetro: $1 = término parcial
SELECT DISTINCT
  palabra,
  'tramite' as fuente,
  COUNT(*) as frecuencia
FROM (
  SELECT unnest(string_to_array(lower(nombre), ' ')) as palabra
  FROM ingestion.tramites
  WHERE lower(nombre) LIKE lower($1 || '%')
) palabras
WHERE length(palabra) > 2
GROUP BY palabra

UNION ALL

SELECT DISTINCT
  palabra,
  'opa' as fuente,
  COUNT(*) as frecuencia
FROM (
  SELECT unnest(string_to_array(lower(nombre), ' ')) as palabra
  FROM ingestion.opas
  WHERE lower(nombre) LIKE lower($1 || '%')
) palabras
WHERE length(palabra) > 2
GROUP BY palabra

ORDER BY frecuencia DESC, palabra
LIMIT 10;
