(()=>{var e={};e.id=9437,e.ids=[9437],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24768:(e,r,t)=>{"use strict";t.d(r,{y8:()=>o});var s=t(2492),n=t(62518);let i="https://hndowofzjzjoljnapokv.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(!i||!a)throw Error("Missing Supabase environment variables");let o=async()=>{let{cookies:e}=await t.e(5208).then(t.bind(t,65208)),r=e();return(0,n.createServerComponentClient)({cookies:()=>r})};(0,s.UU)(i,a)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57657:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,PUT:()=>u});var n=t(48106),i=t(48819),a=t(12050),o=t(4235),d=t(24768);async function p(e,{params:r}){try{let{id:t}=r,{searchParams:s}=new URL(e.url),n="true"===s.get("includeContent"),i=(0,d.y8)(),{data:a,error:p}=await i.from("dependencias_view").select("*").eq("id",t).single();if(p||!a)return o.NextResponse.json({error:"Dependencia no encontrada"},{status:404});let{data:u,error:c}=await i.from("subdependencias_detail_view").select("*").eq("dependencia_id",t).order("nombre");if(c)return console.error("Error fetching subdependencias:",c),o.NextResponse.json({error:"Error al obtener subdependencias"},{status:500});let l=null;if(n){let e=u?.map(e=>e.id)||[],{data:r}=await i.from("tramites_view").select("*").in("subdependencia_id",e).order("nombre"),{data:t}=await i.from("opas_view").select("*").in("subdependencia_id",e).order("nombre"),{data:s}=await i.from("faqs_view").select("*").in("subdependencia_id",e).order("tema",{ascending:!0});l={tramites:r||[],opas:t||[],faqs:s||[]}}return o.NextResponse.json({success:!0,data:{dependencia:a,subdependencias:u||[],contenido:l}})}catch(e){return console.error("Unexpected error in dependencia detail API:",e),o.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function u(e,{params:r}){try{let{id:t}=r,s=await e.json(),n=(0,d.y8)(),{data:{user:i},error:a}=await n.auth.getUser();if(a||!i)return o.NextResponse.json({error:"No autorizado"},{status:401});let{data:p}=await n.rpc("check_user_permission",{p_user_id:i.id,p_action:"UPDATE",p_table_name:"dependencias",p_dependencia_id:t});if(!p)return o.NextResponse.json({error:"Sin permisos para modificar esta dependencia"},{status:403});let{data:u,error:c}=await n.schema("ingestion").from("dependencias").update({...s,updated_at:new Date().toISOString()}).eq("id",t).select().single();if(c)return console.error("Error updating dependencia:",c),o.NextResponse.json({error:"Error al actualizar dependencia"},{status:500});return o.NextResponse.json({success:!0,data:u})}catch(e){return console.error("Error in dependencia PUT:",e),o.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/dependencias/[id]/route",pathname:"/api/dependencias/[id]",filename:"route",bundlePath:"app/api/dependencias/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\dependencias\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=c;function b(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744,2518],()=>t(57657));module.exports=s})();