'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  ChevronDownIcon, 
  ChevronUpIcon,
  QuestionMarkCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

interface FAQ {
  id: string;
  tema: string;
  pregunta: string;
  respuesta: string;
  dependencia: {
    id: string;
    codigo: string;
    nombre: string;
    sigla: string;
  };
}

interface FAQResponse {
  success: boolean;
  data: FAQ[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
  };
}

interface FAQSectionProps {
  title?: string;
  subtitle?: string;
  limit?: number;
  showViewAll?: boolean;
  className?: string;
}

export default function FAQSection({ 
  title = "Preguntas Frecuentes",
  subtitle = "Encuentra respuestas rápidas a las consultas más comunes",
  limit = 6,
  showViewAll = true,
  className = ""
}: FAQSectionProps) {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  // Fetch FAQs
  const fetchFAQs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/faqs?limit=${limit}`);
      if (!response.ok) {
        throw new Error('Error al cargar las preguntas frecuentes');
      }

      const data: FAQResponse = await response.json();
      setFaqs(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFAQs();
  }, [limit]);

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-xl shadow-md p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando preguntas frecuentes...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-xl shadow-md p-8 ${className}`}>
        <div className="text-center">
          <QuestionMarkCircleIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchFAQs}
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-xl shadow-md p-8 ${className}`}>
      {/* Header */}
      <div className="text-center mb-8">
        <QuestionMarkCircleIcon className="h-12 w-12 text-primary-600 mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          {title}
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          {subtitle}
        </p>
      </div>

      {/* FAQs List */}
      {faqs.length === 0 ? (
        <div className="text-center py-8">
          <QuestionMarkCircleIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No hay preguntas frecuentes disponibles
          </h3>
          <p className="text-gray-600">
            Vuelve más tarde para ver las preguntas más comunes.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {faqs.map((faq) => (
            <div key={faq.id} className="border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleFAQ(faq.id)}
                className="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {faq.pregunta}
                    </h3>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded-full">
                        {faq.tema}
                      </span>
                      <span>
                        {faq.dependencia.nombre}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4">
                    {expandedFAQ === faq.id ? (
                      <ChevronUpIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </button>

              {expandedFAQ === faq.id && (
                <div className="px-6 pb-6 border-t border-gray-100">
                  <div className="pt-4">
                    <div className="prose max-w-none text-gray-700">
                      {faq.respuesta.split('\n').map((paragraph, index) => (
                        <p key={index} className="mb-3 last:mb-0">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* View All Link */}
      {showViewAll && faqs.length > 0 && (
        <div className="mt-8 text-center">
          <Link
            href="/preguntas-frecuentes"
            className="inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-medium transition-colors"
          >
            Ver todas las preguntas frecuentes
            <ArrowRightIcon className="h-4 w-4" />
          </Link>
        </div>
      )}
    </div>
  );
}
