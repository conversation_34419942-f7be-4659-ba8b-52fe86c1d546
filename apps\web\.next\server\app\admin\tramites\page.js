(()=>{var e={};e.id=570,e.ids=[570],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16260:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=s(24332),r=s(48819),i=s(67851),n=s.n(i),l=s(97540),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["tramites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,16615)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\tramites\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,47672)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\tramites\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/tramites/page",pathname:"/admin/tramites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},16615:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\admin\\\\tramites\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\admin\\tramites\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23365:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},26791:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(13486);s(60159);var r=s(76353),i=s(67499);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...s})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31145:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(13486),r=s(60159),i=s(95194),n=s(26791),l=s(39662),d=s(81480),c=s(84396),o=s(74439),m=s(35795),x=s(95827),h=s(99672),p=s(23365),u=s(59327),j=s(42726),g=s(37654),y=s(70393),v=s(49989),f=s.n(v);function b(){let[e,t]=(0,r.useState)([]),[s,v]=(0,r.useState)(!0),[b,N]=(0,r.useState)(null),[k,A]=(0,r.useState)(""),[w,C]=(0,r.useState)(""),[P,M]=(0,r.useState)(""),[E,_]=(0,r.useState)([]),[q,$]=(0,r.useState)(1),[z,S]=(0,r.useState)({page:1,limit:20,total:0,totalPages:0}),D=async()=>{try{v(!0);let e=new URLSearchParams({page:q.toString(),limit:"20"});k&&e.append("search",k),w&&e.append("categoria",w),P&&e.append("estado",P);let s=await fetch(`/api/tramites?${e}`);if(!s.ok)throw Error("Error al cargar tr\xe1mites");let a=await s.json();if(a.success)t(a.data),S(a.pagination);else throw Error(a.error||"Error desconocido")}catch(e){N(e instanceof Error?e.message:"Error desconocido")}finally{v(!1)}},T=e=>{A(e),$(1)},U=()=>{A(""),C(""),M(""),$(1)},J=async e=>{if(confirm("\xbfEst\xe1s seguro de que deseas eliminar este tr\xe1mite?"))try{(await fetch(`/api/tramites/${e}`,{method:"DELETE"})).ok?D():alert("Error al eliminar el tr\xe1mite")}catch(e){console.error("Error deleting tramite:",e),alert("Error al eliminar el tr\xe1mite")}};return s&&0===e.length?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Cargando tr\xe1mites..."})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Gesti\xf3n de Tr\xe1mites"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Administra los tr\xe1mites municipales"})]}),(0,a.jsx)(f(),{href:"/admin/tramites/nuevo",children:(0,a.jsxs)(d.$n,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Nuevo Tr\xe1mite"]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Filtros"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(l.pd,{type:"text",placeholder:"Buscar tr\xe1mites...",value:k,onChange:e=>T(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(c.l6,{value:w||"all",onValueChange:e=>{C("all"===e?"":e),$(1)},children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Todas las categor\xedas"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"all",children:"Todas las categor\xedas"}),E.map(e=>(0,a.jsx)(c.eb,{value:e,children:e},e))]})]}),(0,a.jsxs)(c.l6,{value:P||"all",onValueChange:e=>{M("all"===e?"":e),$(1)},children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Todos los estados"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"all",children:"Todos los estados"}),(0,a.jsx)(c.eb,{value:"activo",children:"Activo"}),(0,a.jsx)(c.eb,{value:"inactivo",children:"Inactivo"}),(0,a.jsx)(c.eb,{value:"borrador",children:"Borrador"})]})]}),(0,a.jsxs)(d.$n,{variant:"outline",onClick:U,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Limpiar Filtros"]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-gray-600",children:["Mostrando ",e.length," de ",z.total," tr\xe1mites"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["P\xe1gina ",z.page," de ",z.totalPages]})]}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Nombre"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Categor\xeda"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Dependencia"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Estado"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Tiempo"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Costo"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900",children:"Acciones"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 line-clamp-1",children:e.nombre}),e.descripcion&&(0,a.jsx)("p",{className:"text-sm text-gray-500 line-clamp-1",children:e.descripcion})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)(n.E,{variant:"secondary",children:e.categoria})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{className:"truncate",children:[e.dependencia_nombre,e.subdependencia_nombre&&` - ${e.subdependencia_nombre}`]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)(n.E,{variant:"activo"===e.estado?"default":"secondary",children:e.estado})}),(0,a.jsx)("td",{className:"py-3 px-4",children:e.tiempo_estimado&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{children:e.tiempo_estimado})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:e.costo&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),(0,a.jsx)("span",{children:e.costo})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)(f(),{href:`/tramites/${e.id}`,children:(0,a.jsx)(d.$n,{variant:"ghost",size:"sm",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})}),(0,a.jsx)(f(),{href:`/admin/tramites/${e.id}/editar`,children:(0,a.jsx)(d.$n,{variant:"ghost",size:"sm",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})}),(0,a.jsx)(d.$n,{variant:"ghost",size:"sm",onClick:()=>J(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}),z.totalPages>1&&(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(d.$n,{variant:"outline",onClick:()=>$(Math.max(1,q-1)),disabled:1===q||s,children:"Anterior"}),(0,a.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,z.totalPages)},(e,t)=>{let r=t+1;return(0,a.jsx)(d.$n,{variant:q===r?"default":"outline",size:"sm",onClick:()=>$(r),disabled:s,children:r},r)})}),(0,a.jsx)(d.$n,{variant:"outline",onClick:()=>$(Math.min(z.totalPages,q+1)),disabled:q===z.totalPages||s,children:"Siguiente"})]}),0===e.length&&!s&&(0,a.jsx)(i.Zp,{children:(0,a.jsxs)(i.Wu,{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCC4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No se encontraron tr\xe1mites"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:k||w||P?"Intenta ajustar los filtros de b\xfasqueda":"Comienza creando tu primer tr\xe1mite"}),!k&&!w&&!P&&(0,a.jsx)(f(),{href:"/admin/tramites/nuevo",children:(0,a.jsxs)(d.$n,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Crear Primer Tr\xe1mite"]})}),(k||w||P)&&(0,a.jsx)(d.$n,{onClick:U,children:"Limpiar Filtros"})]})})]})}},33873:e=>{"use strict";e.exports=require("path")},35795:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},37654:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},39662:(e,t,s)=>{"use strict";s.d(t,{pd:()=>a.pd});var a=s(91823)},40988:(e,t,s)=>{Promise.resolve().then(s.bind(s,31145))},42726:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},59327:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70393:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},74439:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},82844:(e,t,s)=>{Promise.resolve().then(s.bind(s,16615))},84396:(e,t,s)=>{"use strict";s.d(t,{bq:()=>a.bq,eb:()=>a.eb,gC:()=>a.gC,l6:()=>a.l6,yv:()=>a.yv});var a=s(91823)},95827:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},99672:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(84667).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[191,7118,114,684,5194,7269],()=>s(16260));module.exports=a})();