(()=>{var e={};e.id=6999,e.ids=[6999],e.modules={56:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>c});var o=t(48106),n=t(48819),a=t(12050),i=t(4235),u=t(24768);async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("search"),s=r.get("categoria"),o=r.get("dependencia"),n=r.get("subdependencia"),a=parseInt(r.get("limit")||"20"),p=parseInt(r.get("offset")||"0"),c=(0,u.y8)().from("opas_view").select("*",{count:"exact"});t&&(c=c.or(`nombre.ilike.%${t}%,descripcion.ilike.%${t}%,objetivo.ilike.%${t}%`)),s&&(c=c.eq("categoria",s)),o&&(c=c.eq("dependencia_id",o)),n&&(c=c.eq("subdependencia_id",n)),c=c.order("dependencia_nombre").order("subdependencia_nombre").order("nombre").range(p,p+a-1);let{data:d,error:l,count:x}=await c;if(l)return console.error("Error fetching OPAs:",l),i.NextResponse.json({error:"Error al obtener OPAs"},{status:500});return i.NextResponse.json({success:!0,data:d,pagination:{limit:a,offset:p,total:x||0,hasMore:(x||0)>p+a}})}catch(e){return console.error("Unexpected error in OPAs API:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}async function c(e){try{let r=await e.json(),{action:t}=r,s=(0,u.y8)();if("get_categories"===t){let{data:e,error:r}=await s.from("opas_view").select("categoria").not("categoria","is",null);if(r)throw r;let t=[...new Set(e?.map(e=>e.categoria))].filter(Boolean);return i.NextResponse.json({success:!0,data:t})}if(!t){let{data:{user:e},error:t}=await s.auth.getUser();if(t||!e)return i.NextResponse.json({error:"No autorizado"},{status:401});let{data:o}=await s.rpc("check_user_permission",{p_user_id:e.id,p_action:"INSERT",p_table_name:"opas"});if(!o)return i.NextResponse.json({error:"Sin permisos para crear OPAs"},{status:403});let{nombre:n,descripcion:a,objetivo:u,alcance:p,normatividad:c,responsable:d,tiempo_respuesta:l,canales_atencion:x,palabras_clave:g,categoria:m,subdependencia_id:b,metadata:h}=r;if(!n||!b)return i.NextResponse.json({error:"Nombre y subdependencia son requeridos"},{status:400});let{data:f,error:j}=await s.schema("ingestion").from("opas").insert({nombre:n,descripcion:a,objetivo:u,alcance:p,normatividad:c,responsable:d,tiempo_respuesta:l,canales_atencion:x,palabras_clave:g,categoria:m,subdependencia_id:b,metadata:h,activo:!0,fuente_original:"portal_admin"}).select().single();if(j)return console.error("Error creating OPA:",j),i.NextResponse.json({error:"Error al crear OPA"},{status:500});return i.NextResponse.json({success:!0,data:f})}return i.NextResponse.json({error:"Acci\xf3n no v\xe1lida"},{status:400})}catch(e){return console.error("Error in OPAs POST:",e),i.NextResponse.json({error:"Error interno del servidor"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/opas/route",pathname:"/api/opas",filename:"route",bundlePath:"app/api/opas/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\opas\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:g}=d;function m(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},24768:(e,r,t)=>{"use strict";t.d(r,{y8:()=>i});var s=t(2492),o=t(62518);let n="https://hndowofzjzjoljnapokv.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(!n||!a)throw Error("Missing Supabase environment variables");let i=async()=>{let{cookies:e}=await t.e(5208).then(t.bind(t,65208)),r=e();return(0,o.createServerComponentClient)({cookies:()=>r})};(0,s.UU)(n,a)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,2492,3744,2518],()=>t(56));module.exports=s})();