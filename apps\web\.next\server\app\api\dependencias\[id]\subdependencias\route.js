(()=>{var e={};e.id=8437,e.ids=[8437],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18275:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>q,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{GET:()=>p});var n=s(48106),i=s(48819),o=s(12050),u=s(4235);let a=(0,s(2492).UU)("https://hndowofzjzjoljnapokv.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function p(e,{params:r}){try{let e=r.id;if(!e)return u.NextResponse.json({success:!1,error:"ID de dependencia requerido"},{status:400});let{data:s,error:t}=await a.from("subdependencias").select("id, nombre, descripcion").eq("dependencia_id",e).order("nombre");if(t)return console.error("Error fetching subdependencias:",t),u.NextResponse.json({success:!1,error:"Error al obtener subdependencias",details:t.message},{status:500});return u.NextResponse.json({success:!0,data:s||[]})}catch(e){return console.error("Unexpected error in subdependencias API:",e),u.NextResponse.json({success:!1,error:"Error interno del servidor",details:e instanceof Error?e.message:"Error desconocido"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/dependencias/[id]/subdependencias/route",pathname:"/api/dependencias/[id]/subdependencias",filename:"route",bundlePath:"app/api/dependencias/[id]/subdependencias/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\dependencias\\[id]\\subdependencias\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:c,workUnitAsyncStorage:x,serverHooks:l}=d;function q(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:x})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[191,2492,3744],()=>s(18275));module.exports=t})();