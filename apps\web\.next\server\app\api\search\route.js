(()=>{var e={};e.id=6202,e.ids=[6202],e.modules={1241:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56734:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var i={};t.r(i),t.d(i,{GET:()=>c,POST:()=>d});var s=t(48106),a=t(48819),n=t(12050),o=t(4235),p=t(73474);async function c(e){try{let r=(0,p.y8)(),{searchParams:t}=new URL(e.url),i=t.get("q")||t.get("query"),s=parseInt(t.get("limit")||"20"),a=parseInt(t.get("offset")||"0"),n=t.get("tipo");if(!i||0===i.trim().length)return o.NextResponse.json({error:"Search query is required"},{status:400});let{data:c,error:d}=await r.from("tramites_view").select("*").or(`nombre.ilike.%${i}%,descripcion.ilike.%${i}%`).limit(Math.floor(s/3)),{data:u,error:l}=await r.from("faqs_view").select("*").or(`pregunta.ilike.%${i}%,respuesta.ilike.%${i}%`).limit(Math.floor(s/3)),{data:m,error:g}=await r.from("opas_view").select("*").or(`nombre.ilike.%${i}%,descripcion.ilike.%${i}%,objetivo.ilike.%${i}%`).limit(Math.floor(s/3));if(d||l||g)return console.error("Error in unified search:",d||l||g),o.NextResponse.json({error:"Search failed",details:(d||l||g)?.message},{status:500});let h=(c||[]).map(e=>({id:e.id,tipo:"tramite",titulo:e.nombre,contenido:e.descripcion,dependencia:{id:e.dependencia_id,nombre:e.dependencia_nombre,sigla:e.dependencia_sigla},categoria:e.categoria||"General"})),f=(u||[]).map(e=>({id:e.id,tipo:"faq",titulo:e.pregunta,contenido:e.respuesta,dependencia:{id:e.dependencia_id,nombre:e.dependencia_nombre,sigla:e.dependencia_sigla},categoria:e.tema||"General"})),q=(m||[]).map(e=>({id:e.id,tipo:"opa",titulo:e.nombre,contenido:e.descripcion,dependencia:{id:e.dependencia_id,nombre:e.dependencia_nombre,sigla:""},categoria:e.categoria||"General"})),x=[...h,...f,...q],b=n?x.filter(e=>e.tipo===n):x,v={faqs:b.filter(e=>"faq"===e.tipo),tramites:b.filter(e=>"tramite"===e.tipo),opas:b.filter(e=>"opa"===e.tipo)};return o.NextResponse.json({success:!0,query:i,data:b,grouped:v,pagination:{limit:s,offset:a,total:b.length},stats:{totalResults:b.length,faqCount:v.faqs.length,tramiteCount:v.tramites.length,opaCount:v.opas.length}})}catch(e){return console.error("Unexpected error in search API:",e),o.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(e){try{let{query:r,filters:t={},limit:i=20,offset:s=0}=await e.json();if(!r||0===r.trim().length)return o.NextResponse.json({error:"Search query is required"},{status:400});let a=(0,p.y8)(),n=[];if(!t.tipo||"faq"===t.tipo){let e=a.schema("ingestion").from("faqs").select(`
          id,
          tema,
          pregunta,
          respuesta,
          dependencias:dependencia_id (nombre),
          subdependencias:subdependencia_id (nombre)
        `).eq("activo",!0).textSearch("vector_busqueda",r,{config:"spanish"});t.dependencia&&(e=e.eq("dependencia_id",t.dependencia)),n.push(e.then(({data:e,error:r})=>({type:"faq",data:e?.map(e=>({...e,tipo:"faq",titulo:e.pregunta,contenido:e.respuesta}))||[],error:r})))}if(!t.tipo||"tramite"===t.tipo){let e=a.schema("ingestion").from("tramites").select(`
          id,
          nombre,
          descripcion,
          categoria,
          dependencias:dependencia_id (nombre),
          subdependencias:subdependencia_id (nombre)
        `).eq("activo",!0).textSearch("vector_busqueda",r,{config:"spanish"});t.dependencia&&(e=e.eq("dependencia_id",t.dependencia)),t.categoria&&(e=e.eq("categoria",t.categoria)),n.push(e.then(({data:e,error:r})=>({type:"tramite",data:e?.map(e=>({...e,tipo:"tramite",titulo:e.nombre,contenido:e.descripcion}))||[],error:r})))}let c=await Promise.all(n),d=c.filter(e=>e.error);if(d.length>0)return console.error("Search errors:",d),o.NextResponse.json({error:"Search failed",details:d.map(e=>e.error?.message)},{status:500});let u=c.flatMap(e=>e.data||[]),l=u.slice(s,s+i);return o.NextResponse.json({success:!0,query:r,filters:t,data:l,pagination:{limit:i,offset:s,total:u.length}})}catch(e){return console.error("Error in advanced search:",e),o.NextResponse.json({error:"Search failed"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/search/route",pathname:"/api/search",filename:"route",bundlePath:"app/api/search/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\api\\search\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:g}=u;function h(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72088:()=>{},73474:(e,r,t)=>{"use strict";t.d(r,{y8:()=>n});var i=t(2492);let s="https://hndowofzjzjoljnapokv.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY";if(process.env.SUPABASE_SERVICE_ROLE_KEY,!s||!a)throw Error("Missing Supabase environment variables");function n(){return(0,i.UU)(s,a)}n()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80408:()=>{},81630:e=>{"use strict";e.exports=require("http")},87032:()=>{},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[191,2492,3744],()=>t(56734));module.exports=i})();