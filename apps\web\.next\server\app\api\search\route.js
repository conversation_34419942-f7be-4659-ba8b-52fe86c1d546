/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/search/route";
exports.ids = ["app/api/search/route"];
exports.modules = {

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsearch%2Froute&page=%2Fapi%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsearch%2Froute&page=%2Fapi%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_search_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/search/route.ts */ \"(rsc)/./app/api/search/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/search/route\",\n        pathname: \"/api/search\",\n        filename: \"route\",\n        bundlePath: \"app/api/search/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\api\\\\search\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Juan_Pulgarin_Documents_augment_projects_chia_next_apps_web_app_api_search_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsearch%2Froute&page=%2Fapi%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/search/route.ts":
/*!*********************************!*\
  !*** ./app/api/search/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        const { searchParams } = new URL(request.url);\n        // Get query parameters\n        const query = searchParams.get('q') || searchParams.get('query');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const offset = parseInt(searchParams.get('offset') || '0');\n        const tipo = searchParams.get('tipo'); // 'faq', 'tramite', 'opa'\n        if (!query || query.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search query is required'\n            }, {\n                status: 400\n            });\n        }\n        // Search in tramites\n        const { data: tramites, error: tramitesError } = await supabase.from('tramites_view').select('*').or(`nombre.ilike.%${query}%,descripcion.ilike.%${query}%`).limit(Math.floor(limit / 2));\n        // Search in FAQs\n        const { data: faqs, error: faqsError } = await supabase.from('faqs_view').select('*').or(`pregunta.ilike.%${query}%,respuesta.ilike.%${query}%`).limit(Math.floor(limit / 2));\n        if (tramitesError || faqsError) {\n            console.error('Error in unified search:', tramitesError || faqsError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search failed',\n                details: (tramitesError || faqsError)?.message\n            }, {\n                status: 500\n            });\n        }\n        // Combine and transform results\n        const tramiteResults = (tramites || []).map((tramite)=>({\n                id: tramite.id,\n                tipo: 'tramite',\n                titulo: tramite.nombre,\n                contenido: tramite.descripcion,\n                dependencia: {\n                    id: tramite.dependencia_id,\n                    nombre: tramite.dependencia_nombre,\n                    sigla: tramite.dependencia_sigla\n                },\n                categoria: tramite.categoria || 'General'\n            }));\n        const faqResults = (faqs || []).map((faq)=>({\n                id: faq.id,\n                tipo: 'faq',\n                titulo: faq.pregunta,\n                contenido: faq.respuesta,\n                dependencia: {\n                    id: faq.dependencia_id,\n                    nombre: faq.dependencia_nombre,\n                    sigla: faq.dependencia_sigla\n                },\n                categoria: faq.tema || 'General'\n            }));\n        // Combine all results\n        const allResults = [\n            ...tramiteResults,\n            ...faqResults\n        ];\n        // Filter by type if specified\n        const filteredResults = tipo ? allResults.filter((r)=>r.tipo === tipo) : allResults;\n        // Group results by type for better presentation\n        const groupedResults = {\n            faqs: filteredResults.filter((r)=>r.tipo === 'faq'),\n            tramites: filteredResults.filter((r)=>r.tipo === 'tramite'),\n            opas: []\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            query: query,\n            data: filteredResults,\n            grouped: groupedResults,\n            pagination: {\n                limit,\n                offset,\n                total: filteredResults.length\n            },\n            stats: {\n                totalResults: filteredResults.length,\n                faqCount: groupedResults.faqs.length,\n                tramiteCount: groupedResults.tramites.length,\n                opaCount: 0\n            }\n        });\n    } catch (error) {\n        console.error('Unexpected error in search API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to generate URLs for different result types\nfunction getResultUrl(tipo, id) {\n    switch(tipo){\n        case 'faq':\n            return `/faqs/${id}`;\n        case 'tramite':\n            return `/servicios/tramite/${id}`;\n        case 'opa':\n            return `/procedimientos/${id}`;\n        default:\n            return '#';\n    }\n}\n// Helper function to get category for result types\nfunction getResultCategory(tipo) {\n    switch(tipo){\n        case 'faq':\n            return 'Preguntas Frecuentes';\n        case 'tramite':\n            return 'Trámites y Servicios';\n        case 'opa':\n            return 'Procedimientos Administrativos';\n        default:\n            return 'General';\n    }\n}\n// POST endpoint for advanced search with filters\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { query, filters = {}, limit = 20, offset = 0 } = body;\n        if (!query || query.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search query is required'\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Perform search with additional filters\n        let searchPromises = [];\n        // Search FAQs with filters\n        if (!filters.tipo || filters.tipo === 'faq') {\n            let faqQuery = supabase.schema('ingestion').from('faqs').select(`\n          id,\n          tema,\n          pregunta,\n          respuesta,\n          dependencias:dependencia_id (nombre),\n          subdependencias:subdependencia_id (nombre)\n        `).eq('activo', true).textSearch('vector_busqueda', query, {\n                config: 'spanish'\n            });\n            if (filters.dependencia) {\n                faqQuery = faqQuery.eq('dependencia_id', filters.dependencia);\n            }\n            searchPromises.push(faqQuery.then(({ data, error })=>({\n                    type: 'faq',\n                    data: data?.map((item)=>({\n                            ...item,\n                            tipo: 'faq',\n                            titulo: item.pregunta,\n                            contenido: item.respuesta\n                        })) || [],\n                    error\n                })));\n        }\n        // Search Tramites with filters\n        if (!filters.tipo || filters.tipo === 'tramite') {\n            let tramiteQuery = supabase.schema('ingestion').from('tramites').select(`\n          id,\n          nombre,\n          descripcion,\n          categoria,\n          dependencias:dependencia_id (nombre),\n          subdependencias:subdependencia_id (nombre)\n        `).eq('activo', true).textSearch('vector_busqueda', query, {\n                config: 'spanish'\n            });\n            if (filters.dependencia) {\n                tramiteQuery = tramiteQuery.eq('dependencia_id', filters.dependencia);\n            }\n            if (filters.categoria) {\n                tramiteQuery = tramiteQuery.eq('categoria', filters.categoria);\n            }\n            searchPromises.push(tramiteQuery.then(({ data, error })=>({\n                    type: 'tramite',\n                    data: data?.map((item)=>({\n                            ...item,\n                            tipo: 'tramite',\n                            titulo: item.nombre,\n                            contenido: item.descripcion\n                        })) || [],\n                    error\n                })));\n        }\n        const results = await Promise.all(searchPromises);\n        // Check for errors\n        const errors = results.filter((r)=>r.error);\n        if (errors.length > 0) {\n            console.error('Search errors:', errors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search failed',\n                details: errors.map((e)=>e.error?.message)\n            }, {\n                status: 500\n            });\n        }\n        // Combine and sort results\n        const allResults = results.flatMap((r)=>r.data || []);\n        const sortedResults = allResults.slice(offset, offset + limit);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            query,\n            filters,\n            data: sortedResults,\n            pagination: {\n                limit,\n                offset,\n                total: allResults.length\n            }\n        });\n    } catch (error) {\n        console.error('Error in advanced search:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Search failed'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/search/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminSupabase: () => (/* binding */ createAdminSupabase),\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Environment variables validation\nconst supabaseUrl = \"https://hndowofzjzjoljnapokv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhuZG93b2Z6anpqb2xqbmFwb2t2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3Njc3MDgsImV4cCI6MjA2NzM0MzcwOH0.Q8zEYUAGPF2N5g_CsZYM0sMAPUF9caaBxhhbbvsp7yY\";\nconst serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\n// Admin client for server-side operations (API routes)\nfunction createAdminSupabase() {\n    // For now, use anon key if service role key is not available\n    const key = serviceRoleKey || supabaseAnonKey;\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, key, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n// Regular client for server-side operations\nfunction createServerSupabase() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Default export for API routes\nconst supabase = createServerSupabase();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsearch%2Froute&page=%2Fapi%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CJuan%20Pulgarin%5CDocuments%5Caugment-projects%5Cchia-next%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();