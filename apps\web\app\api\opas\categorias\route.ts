import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Obtener categorías únicas de OPAs
    const { data, error } = await supabase
      .from('opas')
      .select('categoria')
      .not('categoria', 'is', null)
      .not('categoria', 'eq', '')
      .order('categoria');

    if (error) {
      console.error('Error fetching OPAs categories:', error);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Error al obtener categorías de OPAs',
          details: error.message 
        },
        { status: 500 }
      );
    }

    // Extraer categorías únicas
    const categorias = [...new Set(data.map(item => item.categoria))].filter(Boolean);

    return NextResponse.json({
      success: true,
      data: categorias
    });

  } catch (error) {
    console.error('Unexpected error in OPAs categories API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
