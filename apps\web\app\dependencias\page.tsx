'use client';

import { useState, useEffect } from 'react';
import { MainNavigation } from '@/components/layout/MainNavigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Building2, FileText, HelpCircle, Users } from 'lucide-react';
import Link from 'next/link';

interface Dependencia {
  id: string;
  nombre: string;
  descripcion: string;
  total_subdependencias: number;
  total_tramites: number;
  total_opas: number;
  total_faqs: number;
  total_contenido: number;
}

export default function DependenciasPage() {
  const [dependencias, setDependencias] = useState<Dependencia[]>([]);
  const [filteredDependencias, setFilteredDependencias] = useState<Dependencia[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDependencias();
  }, []);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredDependencias(dependencias);
    } else {
      const filtered = dependencias.filter(dep =>
        dep.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dep.descripcion?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredDependencias(filtered);
    }
  }, [searchTerm, dependencias]);

  const fetchDependencias = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/dependencias?limit=50');
      
      if (!response.ok) {
        throw new Error('Error al cargar dependencias');
      }

      const data = await response.json();
      
      if (data.success) {
        setDependencias(data.data);
        setFilteredDependencias(data.data);
      } else {
        throw new Error(data.error || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const getTotalStats = () => {
    return dependencias.reduce((acc, dep) => ({
      tramites: acc.tramites + dep.total_tramites,
      opas: acc.opas + dep.total_opas,
      faqs: acc.faqs + dep.total_faqs,
      contenido: acc.contenido + dep.total_contenido
    }), { tramites: 0, opas: 0, faqs: 0, contenido: 0 });
  };

  const stats = getTotalStats();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Cargando dependencias...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MainNavigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchDependencias}>
                Reintentar
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <MainNavigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Dependencias Municipales
          </h1>
          <p className="text-gray-600">
            Explora las dependencias del municipio de Chía y accede a sus trámites, procedimientos y preguntas frecuentes.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Building2 className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Dependencias</p>
                  <p className="text-2xl font-bold text-gray-900">{dependencias.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Trámites</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.tramites}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">OPAs</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.opas}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <HelpCircle className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">FAQs</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.faqs}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Buscar dependencias..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Dependencies Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDependencias.map((dependencia) => (
            <Card key={dependencia.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">
                  <Link 
                    href={`/dependencias/${dependencia.id}`}
                    className="hover:text-blue-600 transition-colors"
                  >
                    {dependencia.nombre}
                  </Link>
                </CardTitle>
                {dependencia.descripcion && (
                  <CardDescription className="text-sm">
                    {dependencia.descripcion}
                  </CardDescription>
                )}
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Subdependencias:</span>
                    <Badge variant="secondary">{dependencia.total_subdependencias}</Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="text-center">
                      <div className="font-semibold text-green-600">{dependencia.total_tramites}</div>
                      <div className="text-gray-500">Trámites</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-purple-600">{dependencia.total_opas}</div>
                      <div className="text-gray-500">OPAs</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-orange-600">{dependencia.total_faqs}</div>
                      <div className="text-gray-500">FAQs</div>
                    </div>
                  </div>
                  
                  <div className="pt-2 border-t">
                    <Link href={`/dependencias/${dependencia.id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        Ver Detalle
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredDependencias.length === 0 && searchTerm && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No se encontraron dependencias
            </h3>
            <p className="text-gray-600">
              Intenta con otros términos de búsqueda
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
