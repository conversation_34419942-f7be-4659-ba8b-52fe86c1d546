(()=>{var e={};e.id=5516,e.ids=[5516],e.modules={959:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var r=a(13486),t=a(60159),i=a(2984),n=a(68713),c=a(95194),l=a(26791),d=a(81480),x=a(43967),m=a(15998),o=a(72513),h=a(99672),p=a(23365),u=a(59327),j=a(63821),g=a(86e3),N=a(3605),v=a(5447),y=a(49989),f=a.n(y);function b(){(0,i.useParams)().id;let[e,s]=(0,t.useState)(null),[a,y]=(0,t.useState)(!0),[b,A]=(0,t.useState)(null);return a?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.<PERSON>avigation,{}),(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Cargando informaci\xf3n del tr\xe1mite..."})]})})})]}):b||!e?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.MainNavigation,{}),(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-red-500 text-xl mb-4",children:"⚠️"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error al cargar"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:b||"Tr\xe1mite no encontrado"}),(0,r.jsx)(f(),{href:"/tramites",children:(0,r.jsx)(d.$n,{children:"Volver a Tr\xe1mites"})})]})})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.MainNavigation,{}),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-6",children:[(0,r.jsx)(f(),{href:"/tramites",className:"hover:text-blue-600",children:"Tr\xe1mites"}),(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)(f(),{href:`/dependencias/${e.dependencia_id}`,className:"hover:text-blue-600",children:e.dependencia_nombre}),(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-gray-900 truncate",children:e.nombre})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)(f(),{href:"/tramites",children:(0,r.jsxs)(d.$n,{variant:"outline",size:"sm",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Volver a Tr\xe1mites"]})})}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)(o.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:e.nombre}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,r.jsx)(l.E,{variant:"secondary",children:e.categoria}),(0,r.jsx)(l.E,{variant:"activo"===e.estado?"default":"secondary",children:e.estado})]})]})}),e.descripcion&&(0,r.jsx)("p",{className:"text-gray-600 text-lg",children:e.descripcion})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Informaci\xf3n General"})}),(0,r.jsx)(c.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Dependencia"}),(0,r.jsx)("p",{className:"font-medium",children:e.dependencia_nombre}),e.subdependencia_nombre&&(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.subdependencia_nombre})]})]}),e.tiempo_estimado&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Tiempo Estimado"}),(0,r.jsx)("p",{className:"font-medium",children:e.tiempo_estimado})]})]}),e.costo&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Costo"}),(0,r.jsx)("p",{className:"font-medium",children:e.costo})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Modalidad"}),(0,r.jsx)("p",{className:"font-medium",children:e.modalidad})]})]})]})})]}),e.requisitos&&e.requisitos.length>0&&(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Requisitos"}),(0,r.jsx)(c.BT,{children:"Documentos y condiciones necesarias para realizar el tr\xe1mite"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)("ul",{className:"space-y-3",children:e.requisitos.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-green-600 mt-0.5 shrink-0"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})})]}),e.documentos_requeridos&&e.documentos_requeridos.length>0&&(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Documentos Requeridos"}),(0,r.jsx)(c.BT,{children:"Documentos que debe presentar para el tr\xe1mite"})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)("ul",{className:"space-y-3",children:e.documentos_requeridos.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-600 mt-0.5 shrink-0"}),(0,r.jsx)("span",{className:"text-gray-700",children:e})]},s))})})]}),e.observaciones&&(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Observaciones"})}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 text-amber-600 mt-0.5 shrink-0"}),(0,r.jsx)("p",{className:"text-gray-700",children:e.observaciones})]})})]}),e.normativa&&(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Marco Normativo"})}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)("p",{className:"text-gray-700",children:e.normativa})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Acciones"})}),(0,r.jsxs)(c.Wu,{className:"space-y-3",children:[(0,r.jsxs)(d.$n,{className:"w-full",size:"lg",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Iniciar Tr\xe1mite"]}),(0,r.jsxs)(d.$n,{variant:"outline",className:"w-full",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Descargar Formulario"]}),(0,r.jsx)(f(),{href:`/dependencias/${e.dependencia_id}`,children:(0,r.jsxs)(d.$n,{variant:"outline",className:"w-full",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Ver Dependencia"]})})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"\xbfNecesitas Ayuda?"})}),(0,r.jsxs)(c.Wu,{className:"space-y-3",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Para m\xe1s informaci\xf3n sobre este tr\xe1mite, puedes contactar directamente a:"}),(0,r.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.dependencia_nombre}),e.subdependencia_nombre&&(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.subdependencia_nombre})]}),(0,r.jsx)(f(),{href:"/contacto",children:(0,r.jsx)(d.$n,{variant:"outline",size:"sm",className:"w-full",children:"Ver Informaci\xf3n de Contacto"})})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Tr\xe1mites Relacionados"})}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(f(),{href:`/tramites?dependencia=${e.dependencia_id}`,children:(0,r.jsx)(d.$n,{variant:"outline",size:"sm",className:"w-full",children:"Ver M\xe1s Tr\xe1mites de esta Dependencia"})})})]})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3605:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(84667).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},5447:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(84667).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15998:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(84667).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23365:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(84667).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},26224:(e,s,a)=>{Promise.resolve().then(a.bind(a,959))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36168:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>x,routeModule:()=>o,tree:()=>d});var r=a(24332),t=a(48819),i=a(67851),n=a.n(i),c=a(97540),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);a.d(s,l);let d={children:["",{children:["tramites",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,38137)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\tramites\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,34356)),"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,x=["C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\tramites\\[id]\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},o=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/tramites/[id]/page",pathname:"/tramites/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},38137:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-next\\\\apps\\\\web\\\\app\\\\tramites\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\chia-next\\apps\\web\\app\\tramites\\[id]\\page.tsx","default")},59327:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(84667).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63821:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(84667).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},68080:(e,s,a)=>{Promise.resolve().then(a.bind(a,38137))},86e3:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(84667).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[191,7118,114,684,8686,5194,3536],()=>a(36168));module.exports=r})();